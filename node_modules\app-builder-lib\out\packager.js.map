{"version": 3, "file": "packager.js", "sourceRoot": "", "sources": ["../src/packager.ts"], "names": [], "mappings": ";;;AAAA,+CAAoL;AACpL,+DAAwD;AACxD,sDAA+E;AAC/E,mCAAqC;AACrC,uCAAoD;AACpD,8BAA6B;AAC7B,uCAA+B;AAC/B,6BAA4B;AAC5B,gDAA2D;AAC3D,uCAAmC;AACnC,sCAA0C;AAE1C,iCAA+D;AAC/D,oEAA6E;AAE7E,gEAA4D;AAG5D,yDAAsE;AACtE,uDAAmD;AACnD,2DAAgG;AAChG,0CAAqF;AACrF,wDAAkD;AAClD,oEAAwF;AACxF,4DAAuE;AACvE,0DAAyD;AACzD,sCAA8D;AAC9D,uCAA2C;AAC3C,2BAA4C;AAE5C,SAAS,UAAU,CAAC,OAAqB,EAAE,KAAa,EAAE,OAAsC;IAC9F,OAAO,CAAC,EAAE,CAAC,KAAK,EAAE,OAAO,CAAC,CAAA;AAC5B,CAAC;AAED,KAAK,UAAU,mBAAmB,CAAC,aAA4B,EAAE,QAAkB;IACjF,IAAI,SAAS,GAAG,aAAa,CAAC,SAAS,CAAA;IACvC,IAAI,SAAS,IAAI,IAAI,EAAE;QACrB,SAAS,GAAG,SAAS,CAAC,WAAW,EAAE,CAAA;KACpC;IAED,IAAI,WAAW,GAAG,aAAa,CAAC,WAAW,CAAA;IAC3C,IAAI,SAAS,KAAK,UAAU,IAAI,SAAS,IAAI,IAAI,EAAE;QACjD,OAAO,MAAM,kDAA8B,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAA;KACrE;IAED,IAAI,WAAW,IAAI,IAAI,IAAI,WAAW,KAAK,SAAS,EAAE;QACpD,WAAW,GAAG,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAA;KACpC;IAED,MAAM,aAAa,GAAG,GAAG,QAAQ,CAAC,OAAO,CAAC,eAAe,MAAM,CAAA;IAC/D,MAAM,aAAa,GAAG,aAAa,CAAC,eAAe,KAAK,KAAK,CAAA;IAC7D,IAAI,SAAS,KAAK,QAAQ,IAAI,SAAS,KAAK,eAAe,EAAE;QAC3D,OAAO,IAAI,iCAAe,CAAC,WAAW,EAAE,aAAa,EAAE,aAAa,CAAC,CAAA;KACtE;SAAM,IAAI,SAAS,KAAK,OAAO,EAAE;QAChC,OAAO,IAAI,+BAAc,CAAC,WAAW,EAAE,aAAa,EAAE,aAAa,CAAC,CAAA;KACrE;SAAM;QACL,MAAM,IAAI,wCAAyB,CAAC,sBAAsB,SAAS,EAAE,CAAC,CAAA;KACvE;AACH,CAAC;AAED,MAAa,QAAQ;IA6GnB,oCAAoC;IACpC,YAAY,OAAwB,EAAW,oBAAoB,IAAI,wCAAiB,EAAE;QAA3C,sBAAiB,GAAjB,iBAAiB,CAA0B;QAtGlF,cAAS,GAAoB,IAAI,CAAA;QAKjC,kCAA6B,GAAG,KAAK,CAAA;QAMrC,wBAAmB,GAAG,KAAK,CAAA;QAM3B,iBAAY,GAAoB,IAAI,CAAA;QAKpC,mBAAc,GAAyB,IAAI,CAAA;QAMnD,sCAAiC,GAAG,KAAK,CAAA;QAEhC,iBAAY,GAAG,IAAI,qBAAY,EAAE,CAAA;QAE1C,aAAQ,GAAmB,IAAI,CAAA;QAKtB,mBAAc,GAAG,IAAI,qBAAM,CAAC,UAAU,CAAC,CAAA;QAExC,oBAAe,GAAG,IAAI,eAAI,CAA8B,GAAG,EAAE,CAAC,kCAAiB,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC,CAAA;QAEzH,sBAAiB,GAA8D,EAAE,CAAA;QAIzF,gBAAW,GAAG,IAAI,0BAAW,CAAC,kBAAG,CAAC,cAAc,CAAC,CAAA;QAMlD,uBAAkB,GAAG,IAAI,GAAG,EAA4B,CAAA;QAoBhE,2BAAsB,GAA4E,CAAC,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,EAAE;YAC3H,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,MAAM,CAAC,IAAI,IAAI,0BAAmB,CAAC,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;QAC/F,CAAC,CAAA;QAEO,uBAAkB,GAAkB,IAAI,CAAA;QAexC,eAAU,GAAqB,IAAI,CAAA;QAK1B,cAAS,GAA+B,EAAE,CAAA;QAQzD,IAAI,aAAa,IAAI,OAAO,EAAE;YAC5B,MAAM,IAAI,wCAAyB,CAAC,qEAAqE,CAAC,CAAA;SAC3G;QACD,IAAI,eAAe,IAAI,OAAO,EAAE;YAC9B,MAAM,IAAI,wCAAyB,CAAC,qFAAqF,CAAC,CAAA;SAC3H;QAED,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,IAAI,IAAI,GAAG,EAAsC,CAAA;QAChF,IAAI,OAAO,CAAC,OAAO,IAAI,IAAI,EAAE;YAC3B,OAAO,CAAC,OAAO,GAAG,OAAO,CAAA;SAC1B;QAED,SAAS,cAAc,CAAC,QAAkB,EAAE,KAAoB;YAC9D,SAAS,UAAU,CAAC,qBAA8B;gBAChD,MAAM,MAAM,GAAG,KAAK,EAAQ,CAAA;gBAC5B,OAAO,MAAM,CAAC,MAAM,KAAK,CAAC,IAAI,qBAAqB,CAAC,CAAC,CAAC,CAAC,6BAAc,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAA;YAC/F,CAAC;YAED,IAAI,UAAU,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;YACtC,IAAI,UAAU,IAAI,IAAI,EAAE;gBACtB,UAAU,GAAG,IAAI,GAAG,EAAuB,CAAA;gBAC3C,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAA;aAClC;YAED,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;gBACtB,KAAK,MAAM,IAAI,IAAI,UAAU,CAAC,KAAK,CAAC,EAAE;oBACpC,UAAU,CAAC,GAAG,CAAC,IAAI,EAAE,EAAE,CAAC,CAAA;iBACzB;gBACD,OAAM;aACP;YAED,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;gBACxB,MAAM,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAA;gBACvC,IAAI,SAAS,GAAG,CAAC,EAAE;oBACjB,uBAAQ,CAAC,UAAU,EAAE,6BAAc,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,CAAA;iBAClG;qBAAM;oBACL,KAAK,MAAM,IAAI,IAAI,UAAU,CAAC,IAAI,CAAC,EAAE;wBACnC,uBAAQ,CAAC,UAAU,EAAE,IAAI,EAAE,IAAI,CAAC,CAAA;qBACjC;iBACF;aACF;QACH,CAAC;QAED,IAAI,OAAO,CAAC,GAAG,IAAI,IAAI,EAAE;YACvB,cAAc,CAAC,eAAQ,CAAC,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAA;SAC1C;QACD,IAAI,OAAO,CAAC,KAAK,IAAI,IAAI,EAAE;YACzB,cAAc,CAAC,eAAQ,CAAC,KAAK,EAAE,OAAO,CAAC,KAAK,CAAC,CAAA;SAC9C;QACD,IAAI,OAAO,CAAC,GAAG,IAAI,IAAI,EAAE;YACvB,cAAc,CAAC,eAAQ,CAAC,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,CAAA;SAC9C;QAED,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,IAAI,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC,CAAA;QAC/F,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,UAAU,CAAA;QAC9B,IAAI,CAAC,OAAO,GAAG;YACb,GAAG,OAAO;YACV,WAAW,EAAE,OAAO,CAAC,WAAW,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,EAAE,OAAO,CAAC,WAAW,CAAC;SACrG,CAAA;QAED,IAAI;YACF,kBAAG,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,yBAAe,EAAE,EAAE,EAAE,YAAY,EAAE,EAAE,EAAE,kBAAkB,CAAC,CAAA;SAC/E;QAAC,OAAO,CAAC,EAAE;YACV,kCAAkC;YAClC,IAAI,CAAC,CAAC,CAAC,YAAY,cAAc,CAAC,EAAE;gBAClC,MAAM,CAAC,CAAA;aACR;SACF;IACH,CAAC;IA/KD,IAAI,MAAM;QACR,OAAO,IAAI,CAAC,OAAO,CAAA;IACrB,CAAC;IAGD,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,SAAU,CAAA;IACxB,CAAC;IAID,IAAI,+BAA+B;QACjC,OAAO,IAAI,CAAC,6BAA6B,CAAA;IAC3C,CAAC;IAID,IAAI,kBAAkB;QACpB,OAAO,IAAI,CAAC,mBAAmB,CAAA;IACjC,CAAC;IAGD,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,YAAY,CAAA;IAC1B,CAAC;IAID,IAAI,MAAM;QACR,OAAO,IAAI,CAAC,cAAe,CAAA;IAC7B,CAAC;IAOD,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,QAAS,CAAA;IACvB,CAAC;IAYD,IAAI,cAAc;QAChB,OAAO,IAAI,CAAC,eAAe,CAAC,KAAK,CAAA;IACnC,CAAC;IAID,qBAAqB,CAAC,QAAyB;QAC7C,IAAI,GAAG,GAAG,EAAE,CAAA;QACZ,IAAI,oBAAoB,GAAyB,IAAI,CAAA;QACrD,IAAI,QAAQ,IAAI,IAAI,IAAI,IAAI,CAAC,SAAS,CAAC,uBAAuB,IAAI,IAAI,EAAE;YACtE,oBAAoB,GAAG,IAAI,CAAC,SAAS,CAAC,uBAAuB,CAAC,QAAQ,CAAC,CAAA;YACvE,IAAI,oBAAoB,IAAI,IAAI,EAAE;gBAChC,GAAG,IAAI,IAAI,QAAQ,CAAC,IAAI,EAAE,CAAA;aAC3B;SACF;QAED,IAAI,MAAM,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;QAC7C,IAAI,MAAM,IAAI,IAAI,EAAE;YAClB,MAAM,GAAG,8CAAwB,CAAC,IAAI,CAAC,MAAM,EAAE,oBAAoB,CAAC,CAAA;YACpE,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,GAAG,EAAE,MAAM,CAAC,CAAA;SACzC;QACD,OAAO,MAAM,CAAA;IACf,CAAC;IAQD,IAAI,iBAAiB;QACnB,IAAI,MAAM,GAAG,IAAI,CAAC,kBAAkB,CAAA;QACpC,IAAI,MAAM,IAAI,IAAI,EAAE;YAClB,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,6BAA6B,CAAC,CAAA;YAC1E,IAAI,CAAC,kBAAkB,GAAG,MAAM,CAAA;SACjC;QACD,OAAO,MAAM,CAAA;IACf,CAAC;IAED,IAAI,6BAA6B;QAC/B,OAAO,IAAI,CAAC,MAAM,CAAC,WAAY,CAAC,cAAe,CAAA;IACjD,CAAC;IAGD,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,UAAW,CAAA;IACzB,CAAC;IAID,oBAAoB,CAAC,QAA6B;QAChD,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;IAC/B,CAAC;IA0ED,mBAAmB,CAAC,OAA2D;QAC7E,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;IACtC,CAAC;IAED,eAAe,CAAC,OAAyC;QACvD,UAAU,CAAC,IAAI,CAAC,YAAY,EAAE,iBAAiB,EAAE,OAAO,CAAC,CAAA;QACzD,OAAO,IAAI,CAAA;IACb,CAAC;IAED,KAAK,CAAC,wBAAwB,CAAC,KAA2B,EAAE,SAAe;QACzE,kBAAG,CAAC,IAAI,CACN,SAAS,IAAI;YACX,MAAM,EAAE,KAAK,CAAC,qBAAqB;YACnC,IAAI,EAAE,KAAK,CAAC,IAAI,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,mBAAI,CAAC,KAAK,CAAC,IAAI,CAAC;YAClD,IAAI,EAAE,kBAAG,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC;SAC/B,EACD,UAAU,CACX,CAAA;QACD,MAAM,OAAO,GAAG,kCAAe,CAAC,IAAI,CAAC,MAAM,CAAC,oBAAoB,EAAE,sBAAsB,CAAC,CAAA;QACzF,IAAI,OAAO,IAAI,IAAI,EAAE;YACnB,MAAM,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAA;SACtC;IACH,CAAC;IAED;;OAEG;IACH,uBAAuB,CAAC,KAAsB;QAC5C,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,iBAAiB,EAAE,KAAK,CAAC,CAAA;IAClD,CAAC;IAED,KAAK,CAAC,0BAA0B,CAAC,KAAsB;QACrD,MAAM,OAAO,GAAG,kCAAe,CAAC,IAAI,CAAC,MAAM,CAAC,sBAAsB,EAAE,wBAAwB,CAAC,CAAA;QAC7F,IAAI,OAAO,IAAI,IAAI,EAAE;YACnB,MAAM,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAA;SACtC;QAED,IAAI,CAAC,uBAAuB,CAAC,KAAK,CAAC,CAAA;IACrC,CAAC;IAED,KAAK,CAAC,uBAAuB,CAAC,IAAY;QACxC,MAAM,OAAO,GAAG,kCAAe,CAAC,IAAI,CAAC,MAAM,CAAC,mBAAmB,EAAE,qBAAqB,CAAC,CAAA;QACvF,IAAI,OAAO,IAAI,IAAI,EAAE;YACnB,MAAM,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAA;SACrC;IACH,CAAC;IAED,KAAK,CAAC,qBAAqB,CAAC,IAAY;QACtC,MAAM,OAAO,GAAG,kCAAe,CAAC,IAAI,CAAC,MAAM,CAAC,iBAAiB,EAAE,mBAAmB,CAAC,CAAA;QACnF,IAAI,OAAO,IAAI,IAAI,EAAE;YACnB,MAAM,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAA;SACrC;IACH,CAAC;IAED,KAAK,CAAC,KAAK;QACT,IAAI,UAAU,GAAkB,IAAI,CAAA;QACpC,IAAI,iBAAiB,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAA;QAC3C,IAAI,OAAO,iBAAiB,KAAK,QAAQ,EAAE;YACzC,8BAA8B;YAC9B,UAAU,GAAG,iBAAiB,CAAA;YAC9B,iBAAiB,GAAG,IAAI,CAAA;SACzB;aAAM,IAAI,iBAAiB,IAAI,IAAI,IAAI,OAAO,iBAAiB,CAAC,OAAO,KAAK,QAAQ,IAAI,iBAAiB,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;YAChI,UAAU,GAAG,iBAAiB,CAAC,OAAO,CAAA;YACtC,OAAO,iBAAiB,CAAC,OAAO,CAAA;SACjC;QAED,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAA;QAElC,MAAM,cAAc,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,cAAc,CAAC,CAAA;QAC5D,IAAI,CAAC,YAAY,GAAG,MAAM,8BAAoB,CAAC,iCAAe,CAAC,cAAc,CAAC,CAAC,CAAA;QAE/E,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAA;QACpC,MAAM,aAAa,GAAG,MAAM,kBAAS,CAAC,UAAU,EAAE,UAAU,EAAE,iBAAiB,EAAE,IAAI,eAAI,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CAAA;QAC9H,IAAI,kBAAG,CAAC,cAAc,EAAE;YACtB,kBAAG,CAAC,KAAK,CAAC,EAAE,MAAM,EAAE,sBAAsB,CAAC,aAAa,CAAC,EAAE,EAAE,kBAAkB,CAAC,CAAA;SACjF;QAED,IAAI,CAAC,OAAO,GAAG,MAAM,mCAA0B,CAAC,UAAU,EAAE,aAAa,CAAC,WAAY,CAAC,GAAG,CAAC,CAAA;QAC3F,IAAI,CAAC,iCAAiC,GAAG,IAAI,CAAC,OAAO,KAAK,UAAU,CAAA;QAEpE,MAAM,cAAc,GAAG,IAAI,CAAC,iCAAiC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC,CAAC,CAAC,cAAc,CAAA;QAEvH,+CAA+C;QAC/C,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,iCAAiC,EAAE;YACvE,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,WAAW,CAAA;SAClC;aAAM;YACL,IAAI,CAAC,SAAS,GAAG,MAAM,IAAI,CAAC,mDAAmD,CAAC,cAAc,CAAC,CAAA;SAChG;QACD,yBAAU,CAAC,IAAI,CAAC,QAAQ,EAAE,aAAa,CAAC,aAAa,CAAC,CAAA;QAEtD,IAAI,IAAI,CAAC,iCAAiC,EAAE;YAC1C,kBAAG,CAAC,KAAK,CAAC,EAAE,cAAc,EAAE,cAAc,EAAE,EAAE,oCAAoC,CAAC,CAAA;SACpF;QACD,+BAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,WAAW,EAAE,cAAc,EAAE,cAAc,CAAC,CAAA;QAE9E,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,YAAY,CAAC,CAAA;IAC5E,CAAC;IAED,oJAAoJ;IACpJ,KAAK,CAAC,MAAM,CAAC,aAA4B,EAAE,QAAkB,EAAE,WAA4B,EAAE,cAAqC;QAChI,MAAM,uBAAc,CAAC,aAAa,EAAE,IAAI,CAAC,WAAW,CAAC,CAAA;QACrD,IAAI,CAAC,cAAc,GAAG,aAAa,CAAA;QACnC,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAA;QACzB,IAAI,CAAC,YAAY,GAAG,WAAW,CAAA;QAE/B,IAAI,cAAc,IAAI,IAAI,EAAE;YAC1B,IAAI,CAAC,eAAe,CAAC,KAAK,GAAG,OAAO,CAAC,OAAO,CAAC,cAAc,CAAC,CAAA;SAC7D;QAED,IAAI,CAAC,QAAQ,GAAG,IAAI,iBAAO,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;QACvC,IAAI,CAAC,UAAU,GAAG,MAAM,mBAAmB,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAA;QAE9D,MAAM,kCAAkC,GAAG,IAAI,CAAC,OAAO,CACrD,IAAI,CAAC,UAAU,EACf,2BAAW,CAAC,aAAa,CAAC,WAAY,CAAC,MAAO,EAAE,IAAI,EAAE,IAAI,CAAC,QAAQ,EAAE;YACnE,EAAE,EAAE,EAAE;SACP,CAAC,CACH,CAAA;QAED,IAAI,CAAC,IAAI,IAAK,OAAO,CAAC,MAAc,CAAC,KAAK,EAAE;YAC1C,MAAM,mBAAmB,GAAG,IAAI,CAAC,IAAI,CAAC,kCAAkC,EAAE,+BAA+B,CAAC,CAAA;YAC1G,kBAAG,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,kBAAG,CAAC,QAAQ,CAAC,mBAAmB,CAAC,EAAE,EAAE,0BAA0B,CAAC,CAAA;YACjF,MAAM,qBAAU,CAAC,mBAAmB,EAAE,sBAAsB,CAAC,aAAa,CAAC,CAAC,CAAA;SAC7E;QAED,wFAAwF;QACxF,MAAM,aAAa,GAAG,IAAI,GAAG,EAAU,CAAA;QACvC,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,EAAE;YAC3B,IAAI,KAAK,CAAC,IAAI,IAAI,IAAI,EAAE;gBACtB,aAAa,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;aAC9B;QACH,CAAC,CAAC,CAAA;QAEF,IAAI,CAAC,oBAAoB,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC,CAAA;QAC9D,MAAM,iBAAiB,GAAG,MAAM,wBAAc,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,KAAK,IAAI,EAAE;YACxE,IAAI,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE;gBAC9B,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,kCAAkC,EAAE,mBAAmB,CAAC,CAAC,CAAA;aAChG;YAED,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAA;YACxC,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAA;YACzB,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE;gBAChC,MAAM,QAAQ,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE;oBACzB,kBAAG,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,EAAE,gBAAgB,CAAC,CAAA;gBAC1C,CAAC,CAAC,CAAA;aACH;QACH,CAAC,CAAC,CAAA;QAEF,OAAO;YACL,MAAM,EAAE,kCAAkC;YAC1C,aAAa,EAAE,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC;YACxC,iBAAiB;YACjB,aAAa;SACd,CAAA;IACH,CAAC;IAEO,KAAK,CAAC,mDAAmD,CAAC,cAAsB;QACtF,IAAI,IAAI,GAAG,MAAM,8BAAoB,CAAC,iCAAe,CAAC,cAAc,CAAC,CAAC,CAAA;QACtE,IAAI,IAAI,IAAI,IAAI,EAAE;YAChB,OAAO,IAAI,CAAA;SACZ;QAED,IAAI,GAAG,MAAM,8BAAoB,CAAC,mBAAY,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,UAAU,CAAC,EAAE,cAAc,CAAC,CAAC,CAAA;QACvG,IAAI,IAAI,IAAI,IAAI,EAAE;YAChB,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAA;YAC/B,OAAO,IAAI,CAAA;SACZ;QAED,MAAM,IAAI,KAAK,CAAC,mCAAmC,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,EAAE,CAAC,CAAA;IACpF,CAAC;IAEO,KAAK,CAAC,OAAO;QACnB,MAAM,WAAW,GAAG,IAAI,+BAAgB,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAA;QAEhE,MAAM,gBAAgB,GAAG,IAAI,GAAG,EAAiC,CAAA;QACjE,MAAM,cAAc,GAAG,IAAI,GAAG,EAAU,CAAA;QAExC,KAAK,MAAM,CAAC,QAAQ,EAAE,UAAU,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,OAAQ,EAAE;YAC1D,IAAI,IAAI,CAAC,iBAAiB,CAAC,SAAS,EAAE;gBACpC,MAAK;aACN;YAED,IAAI,QAAQ,KAAK,eAAQ,CAAC,GAAG,IAAI,OAAO,CAAC,QAAQ,KAAK,eAAQ,CAAC,OAAO,CAAC,QAAQ,EAAE;gBAC/E,MAAM,IAAI,wCAAyB,CAAC,oGAAoG,CAAC,CAAA;aAC1I;YAED,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAA;YAClD,MAAM,YAAY,GAAwB,IAAI,GAAG,EAAE,CAAA;YACnD,gBAAgB,CAAC,GAAG,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAA;YAE5C,KAAK,MAAM,CAAC,IAAI,EAAE,WAAW,CAAC,IAAI,2CAA2B,CAAC,UAAU,EAAE,QAAQ,EAAE,QAAQ,CAAC,EAAE;gBAC7F,IAAI,IAAI,CAAC,iBAAiB,CAAC,SAAS,EAAE;oBACpC,MAAK;iBACN;gBAED,4CAA4C;gBAC5C,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,EAAE,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,cAAe,CAAC,WAAY,CAAC,MAAO,EAAE,mBAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;gBACzH,MAAM,UAAU,GAAG,6BAAa,CAAC,YAAY,EAAE,WAAW,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,CAAC,WAAW,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAA;gBACjI,MAAM,kBAAkB,CAAC,UAAU,EAAE,cAAc,CAAC,CAAA;gBACpD,MAAM,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,WAAW,CAAC,CAAA;aAC3D;YAED,IAAI,IAAI,CAAC,iBAAiB,CAAC,SAAS,EAAE;gBACpC,MAAK;aACN;YAED,KAAK,MAAM,MAAM,IAAI,YAAY,CAAC,MAAM,EAAE,EAAE;gBAC1C,WAAW,CAAC,OAAO,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,CAAA;aAC1C;SACF;QAED,MAAM,WAAW,CAAC,UAAU,EAAE,CAAA;QAC9B,OAAO,gBAAgB,CAAA;IACzB,CAAC;IAEO,KAAK,CAAC,YAAY,CAAC,QAAkB;QAC3C,IAAI,IAAI,CAAC,OAAO,CAAC,uBAAuB,IAAI,IAAI,EAAE;YAChD,OAAO,IAAI,CAAC,OAAO,CAAC,uBAAuB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAA;SAC5D;QAED,QAAQ,QAAQ,EAAE;YAChB,KAAK,eAAQ,CAAC,GAAG,CAAC,CAAC;gBACjB,MAAM,WAAW,GAAG,CAAC,2CAAa,eAAe,EAAC,CAAC,CAAC,OAAO,CAAA;gBAC3D,OAAO,IAAI,WAAW,CAAC,IAAI,CAAC,CAAA;aAC7B;YAED,KAAK,eAAQ,CAAC,OAAO,CAAC,CAAC;gBACrB,MAAM,WAAW,GAAG,CAAC,2CAAa,eAAe,EAAC,CAAC,CAAC,WAAW,CAAA;gBAC/D,OAAO,IAAI,WAAW,CAAC,IAAI,CAAC,CAAA;aAC7B;YAED,KAAK,eAAQ,CAAC,KAAK;gBACjB,OAAO,IAAI,CAAC,2CAAa,iBAAiB,EAAC,CAAC,CAAC,aAAa,CAAC,IAAI,CAAC,CAAA;YAElE;gBACE,MAAM,IAAI,KAAK,CAAC,qBAAqB,QAAQ,EAAE,CAAC,CAAA;SACnD;IACH,CAAC;IAEM,KAAK,CAAC,sBAAsB,CAAC,QAAkB,EAAE,IAAU;QAChE,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,oBAAoB,EAAE;YAC5E,OAAM;SACP;QAED,MAAM,aAAa,GAAG,EAAE,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,aAAa,EAAE,IAAI,EAAE,CAAA;QAC9E,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAA;QAC1B,IAAI,MAAM,CAAC,cAAc,KAAK,IAAI,EAAE;YAClC,MAAM,qBAAc,CAAC,QAAQ,CAAC,QAAQ,EAAE,mBAAI,CAAC,IAAI,CAAC,EAAE,aAAa,CAAC,CAAA;SACnE;QAED,IAAI,MAAM,CAAC,UAAU,KAAK,KAAK,EAAE;YAC/B,kBAAG,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,4BAA4B,EAAE,EAAE,8BAA8B,CAAC,CAAA;YAClF,OAAM;SACP;QAED,MAAM,WAAW,GAAG,kCAAe,CAAC,MAAM,CAAC,WAAW,EAAE,aAAa,CAAC,CAAA;QACtE,IAAI,WAAW,IAAI,IAAI,EAAE;YACvB,MAAM,mCAAmC,GAAG,MAAM,WAAW,CAAC;gBAC5D,MAAM,EAAE,IAAI,CAAC,MAAM;gBACnB,eAAe,EAAE,IAAI,CAAC,MAAM,CAAC,eAAgB;gBAC7C,QAAQ;gBACR,IAAI,EAAE,mBAAI,CAAC,IAAI,CAAC;aACjB,CAAC,CAAA;YAEF,6GAA6G;YAC7G,IAAI,CAAC,6BAA6B,GAAG,CAAC,mCAAmC,CAAA;YACzE,IAAI,CAAC,mCAAmC,EAAE;gBACxC,OAAM;aACP;SACF;QAED,IAAI,MAAM,CAAC,2BAA2B,KAAK,IAAI,IAAI,QAAQ,CAAC,QAAQ,KAAK,OAAO,CAAC,QAAQ,EAAE;YACzF,kBAAG,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,sEAAsE,EAAE,EAAE,8BAA8B,CAAC,CAAA;SAC7H;aAAM;YACL,MAAM,uBAAgB,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE;gBAC1C,aAAa;gBACb,QAAQ,EAAE,QAAQ,CAAC,QAAQ;gBAC3B,IAAI,EAAE,mBAAI,CAAC,IAAI,CAAC;gBAChB,cAAc,EAAE,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC;aACjD,CAAC,CAAA;SACH;IACH,CAAC;IAED,KAAK,CAAC,SAAS,CAAC,OAAyB;QACvC,MAAM,SAAS,GAAG,kCAAe,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,WAAW,CAAC,CAAA;QACrE,MAAM,QAAQ,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAA;QAC/C,IAAI,SAAS,IAAI,IAAI,EAAE;YACrB,8BAA8B;YAC9B,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;SACzB;QAED,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE;YAC9B,MAAM,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAA;SACxC;IACH,CAAC;CACF;AA5dD,4BA4dC;AAED,SAAS,kBAAkB,CAAC,UAAyB,EAAE,cAA2B;IAChF,MAAM,OAAO,GAAG,IAAI,GAAG,EAAU,CAAA;IACjC,KAAK,MAAM,MAAM,IAAI,UAAU,EAAE;QAC/B,yCAAyC;QACzC,IAAI,MAAM,YAAY,0BAAU,EAAE;YAChC,SAAQ;SACT;QAED,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAA;QAC5B,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;YAC/B,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;SACpB;KACF;IAED,IAAI,OAAO,CAAC,IAAI,KAAK,CAAC,EAAE;QACtB,OAAO,OAAO,CAAC,OAAO,EAAE,CAAA;KACzB;IAED,OAAO,OAAO,CAAC,GAAG,CAChB,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC;SAChB,IAAI,EAAE;SACN,GAAG,CAAC,GAAG,CAAC,EAAE;QACT,OAAO,iBAAM,CAAC,GAAG,CAAC;aACf,IAAI,CAAC,GAAG,EAAE,CAAC,gBAAK,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,oBAAoB,CAAC;aAClD,IAAI,CAAC,GAAG,EAAE,CAAC,cAAc,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAA;IACxC,CAAC,CAAC,CACL,CAAA;AACH,CAAC;AASD,SAAS,sBAAsB,CAAC,aAA4B;IAC1D,MAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,gCAAiB,CAAC,aAAa,CAAC,CAAC,CAAA;IACtD,IAAI,CAAC,CAAC,OAAO,IAAI,IAAI,EAAE;QACrB,CAAC,CAAC,OAAO,GAAG,qBAAqB,CAAA;KAClC;IACD,OAAO,8BAAe,CAAC,CAAC,EAAE,IAAI,CAAC,CAAA;AACjC,CAAC", "sourcesContent": ["import { addValue, Arch, archFromString, AsyncTaskManager, DebugLogger, deepAssign, InvalidConfigurationError, log, safeStringify<PERSON>son, serializeToYaml, TmpDir } from \"builder-util\"\nimport { CancellationToken } from \"builder-util-runtime\"\nimport { executeFinally, orNullIfFileNotExist } from \"builder-util/out/promise\"\nimport { EventEmitter } from \"events\"\nimport { mkdirs, chmod, outputFile } from \"fs-extra\"\nimport * as isCI from \"is-ci\"\nimport { Lazy } from \"lazy-val\"\nimport * as path from \"path\"\nimport { getArtifactArchName } from \"builder-util/out/arch\"\nimport { AppInfo } from \"./appInfo\"\nimport { readAsarJson } from \"./asar/asar\"\nimport { AfterPackContext, Configuration } from \"./configuration\"\nimport { Platform, SourceRepositoryInfo, Target } from \"./core\"\nimport { createElectronFrameworkSupport } from \"./electron/ElectronFramework\"\nimport { Framework } from \"./Framework\"\nimport { LibUiFramework } from \"./frameworks/LibUiFramework\"\nimport { Metadata } from \"./options/metadata\"\nimport { ArtifactBuildStarted, ArtifactCreated, PackagerOptions } from \"./packagerApi\"\nimport { PlatformPackager, resolveFunction } from \"./platformPackager\"\nimport { ProtonFramework } from \"./ProtonFramework\"\nimport { computeArchToTargetNamesMap, createTargets, NoOpTarget } from \"./targets/targetFactory\"\nimport { computeDefaultAppDirectory, getConfig, validateConfig } from \"./util/config\"\nimport { expandMacro } from \"./util/macroExpander\"\nimport { createLazyProductionDeps, NodeModuleDirInfo } from \"./util/packageDependencies\"\nimport { checkMetadata, readPackageJson } from \"./util/packageMetadata\"\nimport { getRepositoryInfo } from \"./util/repositoryInfo\"\nimport { installOrRebuild, nodeGypRebuild } from \"./util/yarn\"\nimport { PACKAGE_VERSION } from \"./version\"\nimport { release as getOsRelease } from \"os\"\n\nfunction addHandler(emitter: EventEmitter, event: string, handler: (...args: Array<any>) => void) {\n  emitter.on(event, handler)\n}\n\nasync function createFrameworkInfo(configuration: Configuration, packager: Packager): Promise<Framework> {\n  let framework = configuration.framework\n  if (framework != null) {\n    framework = framework.toLowerCase()\n  }\n\n  let nodeVersion = configuration.nodeVersion\n  if (framework === \"electron\" || framework == null) {\n    return await createElectronFrameworkSupport(configuration, packager)\n  }\n\n  if (nodeVersion == null || nodeVersion === \"current\") {\n    nodeVersion = process.versions.node\n  }\n\n  const distMacOsName = `${packager.appInfo.productFilename}.app`\n  const isUseLaunchUi = configuration.launchUiVersion !== false\n  if (framework === \"proton\" || framework === \"proton-native\") {\n    return new ProtonFramework(nodeVersion, distMacOsName, isUseLaunchUi)\n  } else if (framework === \"libui\") {\n    return new LibUiFramework(nodeVersion, distMacOsName, isUseLaunchUi)\n  } else {\n    throw new InvalidConfigurationError(`Unknown framework: ${framework}`)\n  }\n}\n\nexport class Packager {\n  readonly projectDir: string\n\n  private _appDir: string\n  get appDir(): string {\n    return this._appDir\n  }\n\n  private _metadata: Metadata | null = null\n  get metadata(): Metadata {\n    return this._metadata!\n  }\n\n  private _nodeModulesHandledExternally = false\n\n  get areNodeModulesHandledExternally(): boolean {\n    return this._nodeModulesHandledExternally\n  }\n\n  private _isPrepackedAppAsar = false\n\n  get isPrepackedAppAsar(): boolean {\n    return this._isPrepackedAppAsar\n  }\n\n  private _devMetadata: Metadata | null = null\n  get devMetadata(): Metadata | null {\n    return this._devMetadata\n  }\n\n  private _configuration: Configuration | null = null\n\n  get config(): Configuration {\n    return this._configuration!\n  }\n\n  isTwoPackageJsonProjectLayoutUsed = false\n\n  readonly eventEmitter = new EventEmitter()\n\n  _appInfo: AppInfo | null = null\n  get appInfo(): AppInfo {\n    return this._appInfo!\n  }\n\n  readonly tempDirManager = new TmpDir(\"packager\")\n\n  private _repositoryInfo = new Lazy<SourceRepositoryInfo | null>(() => getRepositoryInfo(this.projectDir, this.metadata, this.devMetadata))\n\n  private readonly afterPackHandlers: Array<(context: AfterPackContext) => Promise<any> | null> = []\n\n  readonly options: PackagerOptions\n\n  readonly debugLogger = new DebugLogger(log.isDebugEnabled)\n\n  get repositoryInfo(): Promise<SourceRepositoryInfo | null> {\n    return this._repositoryInfo.value\n  }\n\n  private nodeDependencyInfo = new Map<string, Lazy<Array<any>>>()\n\n  getNodeDependencyInfo(platform: Platform | null): Lazy<Array<NodeModuleDirInfo>> {\n    let key = \"\"\n    let excludedDependencies: Array<string> | null = null\n    if (platform != null && this.framework.getExcludedDependencies != null) {\n      excludedDependencies = this.framework.getExcludedDependencies(platform)\n      if (excludedDependencies != null) {\n        key += `-${platform.name}`\n      }\n    }\n\n    let result = this.nodeDependencyInfo.get(key)\n    if (result == null) {\n      result = createLazyProductionDeps(this.appDir, excludedDependencies)\n      this.nodeDependencyInfo.set(key, result)\n    }\n    return result\n  }\n\n  stageDirPathCustomizer: (target: Target, packager: PlatformPackager<any>, arch: Arch) => string = (target, packager, arch) => {\n    return path.join(target.outDir, `__${target.name}-${getArtifactArchName(arch, target.name)}`)\n  }\n\n  private _buildResourcesDir: string | null = null\n\n  get buildResourcesDir(): string {\n    let result = this._buildResourcesDir\n    if (result == null) {\n      result = path.resolve(this.projectDir, this.relativeBuildResourcesDirname)\n      this._buildResourcesDir = result\n    }\n    return result\n  }\n\n  get relativeBuildResourcesDirname(): string {\n    return this.config.directories!.buildResources!\n  }\n\n  private _framework: Framework | null = null\n  get framework(): Framework {\n    return this._framework!\n  }\n\n  private readonly toDispose: Array<() => Promise<void>> = []\n\n  disposeOnBuildFinish(disposer: () => Promise<void>) {\n    this.toDispose.push(disposer)\n  }\n\n  //noinspection JSUnusedGlobalSymbols\n  constructor(options: PackagerOptions, readonly cancellationToken = new CancellationToken()) {\n    if (\"devMetadata\" in options) {\n      throw new InvalidConfigurationError(\"devMetadata in the options is deprecated, please use config instead\")\n    }\n    if (\"extraMetadata\" in options) {\n      throw new InvalidConfigurationError(\"extraMetadata in the options is deprecated, please use config.extraMetadata instead\")\n    }\n\n    const targets = options.targets || new Map<Platform, Map<Arch, Array<string>>>()\n    if (options.targets == null) {\n      options.targets = targets\n    }\n\n    function processTargets(platform: Platform, types: Array<string>) {\n      function commonArch(currentIfNotSpecified: boolean): Array<Arch> {\n        const result = Array<Arch>()\n        return result.length === 0 && currentIfNotSpecified ? [archFromString(process.arch)] : result\n      }\n\n      let archToType = targets.get(platform)\n      if (archToType == null) {\n        archToType = new Map<Arch, Array<string>>()\n        targets.set(platform, archToType)\n      }\n\n      if (types.length === 0) {\n        for (const arch of commonArch(false)) {\n          archToType.set(arch, [])\n        }\n        return\n      }\n\n      for (const type of types) {\n        const suffixPos = type.lastIndexOf(\":\")\n        if (suffixPos > 0) {\n          addValue(archToType, archFromString(type.substring(suffixPos + 1)), type.substring(0, suffixPos))\n        } else {\n          for (const arch of commonArch(true)) {\n            addValue(archToType, arch, type)\n          }\n        }\n      }\n    }\n\n    if (options.mac != null) {\n      processTargets(Platform.MAC, options.mac)\n    }\n    if (options.linux != null) {\n      processTargets(Platform.LINUX, options.linux)\n    }\n    if (options.win != null) {\n      processTargets(Platform.WINDOWS, options.win)\n    }\n\n    this.projectDir = options.projectDir == null ? process.cwd() : path.resolve(options.projectDir)\n    this._appDir = this.projectDir\n    this.options = {\n      ...options,\n      prepackaged: options.prepackaged == null ? null : path.resolve(this.projectDir, options.prepackaged),\n    }\n\n    try {\n      log.info({ version: PACKAGE_VERSION, os: getOsRelease() }, \"electron-builder\")\n    } catch (e) {\n      // error in dev mode without babel\n      if (!(e instanceof ReferenceError)) {\n        throw e\n      }\n    }\n  }\n\n  addAfterPackHandler(handler: (context: AfterPackContext) => Promise<any> | null) {\n    this.afterPackHandlers.push(handler)\n  }\n\n  artifactCreated(handler: (event: ArtifactCreated) => void): Packager {\n    addHandler(this.eventEmitter, \"artifactCreated\", handler)\n    return this\n  }\n\n  async callArtifactBuildStarted(event: ArtifactBuildStarted, logFields?: any): Promise<void> {\n    log.info(\n      logFields || {\n        target: event.targetPresentableName,\n        arch: event.arch == null ? null : Arch[event.arch],\n        file: log.filePath(event.file),\n      },\n      \"building\"\n    )\n    const handler = resolveFunction(this.config.artifactBuildStarted, \"artifactBuildStarted\")\n    if (handler != null) {\n      await Promise.resolve(handler(event))\n    }\n  }\n\n  /**\n   * Only for sub artifacts (update info), for main artifacts use `callArtifactBuildCompleted`.\n   */\n  dispatchArtifactCreated(event: ArtifactCreated): void {\n    this.eventEmitter.emit(\"artifactCreated\", event)\n  }\n\n  async callArtifactBuildCompleted(event: ArtifactCreated): Promise<void> {\n    const handler = resolveFunction(this.config.artifactBuildCompleted, \"artifactBuildCompleted\")\n    if (handler != null) {\n      await Promise.resolve(handler(event))\n    }\n\n    this.dispatchArtifactCreated(event)\n  }\n\n  async callAppxManifestCreated(path: string): Promise<void> {\n    const handler = resolveFunction(this.config.appxManifestCreated, \"appxManifestCreated\")\n    if (handler != null) {\n      await Promise.resolve(handler(path))\n    }\n  }\n\n  async callMsiProjectCreated(path: string): Promise<void> {\n    const handler = resolveFunction(this.config.msiProjectCreated, \"msiProjectCreated\")\n    if (handler != null) {\n      await Promise.resolve(handler(path))\n    }\n  }\n\n  async build(): Promise<BuildResult> {\n    let configPath: string | null = null\n    let configFromOptions = this.options.config\n    if (typeof configFromOptions === \"string\") {\n      // it is a path to config file\n      configPath = configFromOptions\n      configFromOptions = null\n    } else if (configFromOptions != null && typeof configFromOptions.extends === \"string\" && configFromOptions.extends.includes(\".\")) {\n      configPath = configFromOptions.extends\n      delete configFromOptions.extends\n    }\n\n    const projectDir = this.projectDir\n\n    const devPackageFile = path.join(projectDir, \"package.json\")\n    this._devMetadata = await orNullIfFileNotExist(readPackageJson(devPackageFile))\n\n    const devMetadata = this.devMetadata\n    const configuration = await getConfig(projectDir, configPath, configFromOptions, new Lazy(() => Promise.resolve(devMetadata)))\n    if (log.isDebugEnabled) {\n      log.debug({ config: getSafeEffectiveConfig(configuration) }, \"effective config\")\n    }\n\n    this._appDir = await computeDefaultAppDirectory(projectDir, configuration.directories!.app)\n    this.isTwoPackageJsonProjectLayoutUsed = this._appDir !== projectDir\n\n    const appPackageFile = this.isTwoPackageJsonProjectLayoutUsed ? path.join(this.appDir, \"package.json\") : devPackageFile\n\n    // tslint:disable:prefer-conditional-expression\n    if (this.devMetadata != null && !this.isTwoPackageJsonProjectLayoutUsed) {\n      this._metadata = this.devMetadata\n    } else {\n      this._metadata = await this.readProjectMetadataIfTwoPackageStructureOrPrepacked(appPackageFile)\n    }\n    deepAssign(this.metadata, configuration.extraMetadata)\n\n    if (this.isTwoPackageJsonProjectLayoutUsed) {\n      log.debug({ devPackageFile, appPackageFile }, \"two package.json structure is used\")\n    }\n    checkMetadata(this.metadata, this.devMetadata, appPackageFile, devPackageFile)\n\n    return await this._build(configuration, this._metadata, this._devMetadata)\n  }\n\n  // external caller of this method always uses isTwoPackageJsonProjectLayoutUsed=false and appDir=projectDir, no way (and need) to use another values\n  async _build(configuration: Configuration, metadata: Metadata, devMetadata: Metadata | null, repositoryInfo?: SourceRepositoryInfo): Promise<BuildResult> {\n    await validateConfig(configuration, this.debugLogger)\n    this._configuration = configuration\n    this._metadata = metadata\n    this._devMetadata = devMetadata\n\n    if (repositoryInfo != null) {\n      this._repositoryInfo.value = Promise.resolve(repositoryInfo)\n    }\n\n    this._appInfo = new AppInfo(this, null)\n    this._framework = await createFrameworkInfo(this.config, this)\n\n    const commonOutDirWithoutPossibleOsMacro = path.resolve(\n      this.projectDir,\n      expandMacro(configuration.directories!.output!, null, this._appInfo, {\n        os: \"\",\n      })\n    )\n\n    if (!isCI && (process.stdout as any).isTTY) {\n      const effectiveConfigFile = path.join(commonOutDirWithoutPossibleOsMacro, \"builder-effective-config.yaml\")\n      log.info({ file: log.filePath(effectiveConfigFile) }, \"writing effective config\")\n      await outputFile(effectiveConfigFile, getSafeEffectiveConfig(configuration))\n    }\n\n    // because artifact event maybe dispatched several times for different publish providers\n    const artifactPaths = new Set<string>()\n    this.artifactCreated(event => {\n      if (event.file != null) {\n        artifactPaths.add(event.file)\n      }\n    })\n\n    this.disposeOnBuildFinish(() => this.tempDirManager.cleanup())\n    const platformToTargets = await executeFinally(this.doBuild(), async () => {\n      if (this.debugLogger.isEnabled) {\n        await this.debugLogger.save(path.join(commonOutDirWithoutPossibleOsMacro, \"builder-debug.yml\"))\n      }\n\n      const toDispose = this.toDispose.slice()\n      this.toDispose.length = 0\n      for (const disposer of toDispose) {\n        await disposer().catch(e => {\n          log.warn({ error: e }, \"cannot dispose\")\n        })\n      }\n    })\n\n    return {\n      outDir: commonOutDirWithoutPossibleOsMacro,\n      artifactPaths: Array.from(artifactPaths),\n      platformToTargets,\n      configuration,\n    }\n  }\n\n  private async readProjectMetadataIfTwoPackageStructureOrPrepacked(appPackageFile: string): Promise<Metadata> {\n    let data = await orNullIfFileNotExist(readPackageJson(appPackageFile))\n    if (data != null) {\n      return data\n    }\n\n    data = await orNullIfFileNotExist(readAsarJson(path.join(this.projectDir, \"app.asar\"), \"package.json\"))\n    if (data != null) {\n      this._isPrepackedAppAsar = true\n      return data\n    }\n\n    throw new Error(`Cannot find package.json in the ${path.dirname(appPackageFile)}`)\n  }\n\n  private async doBuild(): Promise<Map<Platform, Map<string, Target>>> {\n    const taskManager = new AsyncTaskManager(this.cancellationToken)\n\n    const platformToTarget = new Map<Platform, Map<string, Target>>()\n    const createdOutDirs = new Set<string>()\n\n    for (const [platform, archToType] of this.options.targets!) {\n      if (this.cancellationToken.cancelled) {\n        break\n      }\n\n      if (platform === Platform.MAC && process.platform === Platform.WINDOWS.nodeName) {\n        throw new InvalidConfigurationError(\"Build for macOS is supported only on macOS, please see https://electron.build/multi-platform-build\")\n      }\n\n      const packager = await this.createHelper(platform)\n      const nameToTarget: Map<string, Target> = new Map()\n      platformToTarget.set(platform, nameToTarget)\n\n      for (const [arch, targetNames] of computeArchToTargetNamesMap(archToType, packager, platform)) {\n        if (this.cancellationToken.cancelled) {\n          break\n        }\n\n        // support os and arch macro in output value\n        const outDir = path.resolve(this.projectDir, packager.expandMacro(this._configuration!.directories!.output!, Arch[arch]))\n        const targetList = createTargets(nameToTarget, targetNames.length === 0 ? packager.defaultTarget : targetNames, outDir, packager)\n        await createOutDirIfNeed(targetList, createdOutDirs)\n        await packager.pack(outDir, arch, targetList, taskManager)\n      }\n\n      if (this.cancellationToken.cancelled) {\n        break\n      }\n\n      for (const target of nameToTarget.values()) {\n        taskManager.addTask(target.finishBuild())\n      }\n    }\n\n    await taskManager.awaitTasks()\n    return platformToTarget\n  }\n\n  private async createHelper(platform: Platform): Promise<PlatformPackager<any>> {\n    if (this.options.platformPackagerFactory != null) {\n      return this.options.platformPackagerFactory(this, platform)\n    }\n\n    switch (platform) {\n      case Platform.MAC: {\n        const helperClass = (await import(\"./macPackager\")).default\n        return new helperClass(this)\n      }\n\n      case Platform.WINDOWS: {\n        const helperClass = (await import(\"./winPackager\")).WinPackager\n        return new helperClass(this)\n      }\n\n      case Platform.LINUX:\n        return new (await import(\"./linuxPackager\")).LinuxPackager(this)\n\n      default:\n        throw new Error(`Unknown platform: ${platform}`)\n    }\n  }\n\n  public async installAppDependencies(platform: Platform, arch: Arch): Promise<any> {\n    if (this.options.prepackaged != null || !this.framework.isNpmRebuildRequired) {\n      return\n    }\n\n    const frameworkInfo = { version: this.framework.version, useCustomDist: true }\n    const config = this.config\n    if (config.nodeGypRebuild === true) {\n      await nodeGypRebuild(platform.nodeName, Arch[arch], frameworkInfo)\n    }\n\n    if (config.npmRebuild === false) {\n      log.info({ reason: \"npmRebuild is set to false\" }, \"skipped dependencies rebuild\")\n      return\n    }\n\n    const beforeBuild = resolveFunction(config.beforeBuild, \"beforeBuild\")\n    if (beforeBuild != null) {\n      const performDependenciesInstallOrRebuild = await beforeBuild({\n        appDir: this.appDir,\n        electronVersion: this.config.electronVersion!,\n        platform,\n        arch: Arch[arch],\n      })\n\n      // If beforeBuild resolves to false, it means that handling node_modules is done outside of electron-builder.\n      this._nodeModulesHandledExternally = !performDependenciesInstallOrRebuild\n      if (!performDependenciesInstallOrRebuild) {\n        return\n      }\n    }\n\n    if (config.buildDependenciesFromSource === true && platform.nodeName !== process.platform) {\n      log.info({ reason: \"platform is different and buildDependenciesFromSource is set to true\" }, \"skipped dependencies rebuild\")\n    } else {\n      await installOrRebuild(config, this.appDir, {\n        frameworkInfo,\n        platform: platform.nodeName,\n        arch: Arch[arch],\n        productionDeps: this.getNodeDependencyInfo(null),\n      })\n    }\n  }\n\n  async afterPack(context: AfterPackContext): Promise<any> {\n    const afterPack = resolveFunction(this.config.afterPack, \"afterPack\")\n    const handlers = this.afterPackHandlers.slice()\n    if (afterPack != null) {\n      // user handler should be last\n      handlers.push(afterPack)\n    }\n\n    for (const handler of handlers) {\n      await Promise.resolve(handler(context))\n    }\n  }\n}\n\nfunction createOutDirIfNeed(targetList: Array<Target>, createdOutDirs: Set<string>): Promise<any> {\n  const ourDirs = new Set<string>()\n  for (const target of targetList) {\n    // noinspection SuspiciousInstanceOfGuard\n    if (target instanceof NoOpTarget) {\n      continue\n    }\n\n    const outDir = target.outDir\n    if (!createdOutDirs.has(outDir)) {\n      ourDirs.add(outDir)\n    }\n  }\n\n  if (ourDirs.size === 0) {\n    return Promise.resolve()\n  }\n\n  return Promise.all(\n    Array.from(ourDirs)\n      .sort()\n      .map(dir => {\n        return mkdirs(dir)\n          .then(() => chmod(dir, 0o755) /* set explicitly */)\n          .then(() => createdOutDirs.add(dir))\n      })\n  )\n}\n\nexport interface BuildResult {\n  readonly outDir: string\n  readonly artifactPaths: Array<string>\n  readonly platformToTargets: Map<Platform, Map<string, Target>>\n  readonly configuration: Configuration\n}\n\nfunction getSafeEffectiveConfig(configuration: Configuration): string {\n  const o = JSON.parse(safeStringifyJson(configuration))\n  if (o.cscLink != null) {\n    o.cscLink = \"<hidden by builder>\"\n  }\n  return serializeToYaml(o, true)\n}\n"]}