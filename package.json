{"name": "voice-to-text-widget", "version": "1.0.0", "description": "Voice-to-text widget with OpenAI integration and multilingual support", "author": "Voice Widget Developer", "main": "dist/main.js", "homepage": ".", "scripts": {"copy-static": "npx copyfiles -u 2 \"src/renderer/*.{html,js,svg}\" dist/renderer/", "start": "npm run build && electron .", "build": "tsc && npm run copy-static", "build-win": "tsc & npx copyfiles -u 2 \"src/renderer/*.{html,js,svg}\" dist/renderer/", "dist": "npm run build && electron-builder", "dist:win": "npm run build && electron-builder --win", "dist:portable": "npm run build && electron-builder --win portable", "pack": "npm run build && electron-builder --dir"}, "build": {"appId": "com.voicewidget.app", "productName": "Voice to Text Widget", "directories": {"output": "dist-electron"}, "files": ["dist/**/*", "node_modules/**/*", "package.json"], "extraResources": [{"from": ".env", "to": ".env", "filter": ["**/*"]}], "win": {"target": [{"target": "nsis", "arch": ["x64"]}, {"target": "portable", "arch": ["x64"]}], "icon": "build/icon.ico", "publisherName": "Voice Widget Developer", "requestedExecutionLevel": "asInvoker"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "Voice to Text Widget"}, "portable": {"artifactName": "VoiceToTextWidget-Portable.exe"}}, "devDependencies": {"@types/node": "^18.16.3", "copyfiles": "^2.4.1", "electron": "^24.1.3", "electron-builder": "^23.6.0", "typescript": "^5.0.4"}, "dependencies": {"dotenv": "^16.0.3", "electron-store": "^8.1.0", "fluent-ffmpeg": "^2.1.3", "form-data": "^4.0.0", "node-fetch": "^3.3.2", "node-key-sender": "^1.0.11"}}