<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Widget</title>
  <style>
    body {
      margin: 0;
      overflow: hidden;
      background: transparent;
    }

    #widget-icon {
      width: 100%;
      height: 100%;
      cursor: pointer;
      position: relative;
    }

    .mic-icon {
      width: 100%;
      height: 100%;
      background: #4CAF50;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 24px;
      transition: all 0.3s ease;
    }

    .mic-icon:hover {
      background: #45a049;
      transform: scale(1.1);
    }

    #tooltip {
      position: absolute;
      top: -25px;
      left: 50%;
      transform: translateX(-50%);
      background-color: #000;
      color: #fff;
      font-size: 10px;
      padding: 2px 6px;
      border-radius: 4px;
      white-space: nowrap;
      display: none;
    }
  </style>
</head>
<body>
  <div id="widget-icon">
    <div class="mic-icon">🎤</div>
    <div id="tooltip">Hold Ctrl+` to record</div>
  </div>
  <script src="widget.js"></script>
</body>
</html>
