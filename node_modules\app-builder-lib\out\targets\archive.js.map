{"version": 3, "file": "archive.js", "sourceRoot": "", "sources": ["../../src/targets/archive.ts"], "names": [], "mappings": ";;;AAAA,wCAAkC;AAClC,+CAA4C;AAC5C,4CAA4D;AAC5D,uCAA+B;AAC/B,6BAA4B;AAC5B,6BAAwD;AAGxD,mCAA2C;AAE3C,gBAAgB;AACT,KAAK,UAAU,GAAG,CACvB,WAAyC,EACzC,MAAc,EACd,OAAe,EACf,YAAoB,EACpB,QAAiB,EACjB,cAAsB;IAEtB,MAAM,OAAO,GAAG,MAAM,cAAc,CAAC,WAAW,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC,CAAA;IACpE,MAAM,OAAO,GAAgC;QAC3C,IAAI,EAAE,OAAO;QACb,QAAQ,EAAE,IAAI;QACd,GAAG,EAAE,YAAY;QACjB,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,IAAI,MAAM,EAAE,CAAC;KAC7C,CAAA;IACD,IAAI,YAAY,GAAG,GAAG,CAAA;IACtB,IAAI,QAAQ,EAAE;QACZ,OAAO,OAAO,CAAC,MAAM,CAAA;QACrB,OAAO,CAAC,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,CAAA;QACxC,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAA;KAC3C;IAED,MAAM,OAAO,CAAC,GAAG,CAAC;QAChB,YAAM,CAAC,OAAO,EAAE,CAAC,YAAY,CAAC,CAAC;QAC/B,6DAA6D;QAC7D,mBAAc,CAAC,OAAO,CAAC;KACxB,CAAC,CAAA;IAEF,IAAI,MAAM,KAAK,QAAQ,EAAE;QACvB,uCAAuC;QACvC,IAAI,QAAQ,GAAG,MAAM,CAAA;QACrB,IAAI,OAAO,CAAC,QAAQ,KAAK,QAAQ,EAAE;YACjC,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,yBAAiB,EAAE,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAA;SACjE;QACD,MAAM,mBAAI,CAAC,QAAQ,EAAE,CAAC,WAAW,KAAK,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,QAAQ,CAAC,qCAAqC,EAAE,OAAO,CAAC,CAAC,CAAA;QACtH,6GAA6G;QAC7G,MAAM,eAAI,CAAC,GAAG,OAAO,KAAK,EAAE,OAAO,CAAC,CAAA;QACpC,OAAM;KACP;IAED,MAAM,IAAI,GAAG,qBAAqB,CAAC,MAAM,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,EAAE;QACvG,aAAa,EAAE,IAAI;QACnB,MAAM,EAAE,SAAS;QACjB,WAAW;KACZ,CAAC,CAAA;IACF,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,CAAA;IAC3B,MAAM,mBAAI,CACR,mBAAO,EACP,IAAI,EACJ;QACE,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC;KAChC,EACD,sBAAO,CAAC,OAAO,CAChB,CAAA;AACH,CAAC;AAtDD,kBAsDC;AA6BD,SAAgB,qBAAqB,CAAC,MAAc,EAAE,UAA0B,EAAE;IAChF,IAAI,SAAS,GAAG,OAAO,CAAC,WAAW,KAAK,OAAO,CAAA;IAC/C,MAAM,IAAI,GAAG,WAAW,CAAC,GAAG,CAAC,CAAA;IAE7B,IAAI,UAAU,GAAG,KAAK,CAAA;IACtB,IAAI,OAAO,CAAC,GAAG,CAAC,kCAAkC,IAAI,IAAI,EAAE;QAC1D,SAAS,GAAG,KAAK,CAAA;QACjB,IAAI,CAAC,IAAI,CAAC,OAAO,OAAO,CAAC,GAAG,CAAC,kCAAkC,EAAE,CAAC,CAAA;QAClE,UAAU,GAAG,IAAI,CAAA;KAClB;IAED,MAAM,KAAK,GAAG,MAAM,KAAK,KAAK,CAAA;IAC9B,IAAI,CAAC,SAAS,EAAE;QACd,IAAI,KAAK,IAAI,OAAO,CAAC,WAAW,KAAK,SAAS,EAAE;YAC9C,gCAAgC;YAChC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,WAAW,CAAC,CAAA;SACnC;QAED,IAAI,CAAC,UAAU,EAAE;YACf,kEAAkE;YAClE,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,IAAI,OAAO,CAAC,WAAW,KAAK,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAA;SAC9E;KACF;IAED,IAAI,OAAO,CAAC,QAAQ,IAAI,IAAI,EAAE;QAC5B,IAAI,CAAC,IAAI,CAAC,OAAO,OAAO,CAAC,QAAQ,GAAG,CAAC,CAAA;KACtC;IAED,8DAA8D;IAC9D,mGAAmG;IACnG,4EAA4E;IAC5E,gJAAgJ;IAChJ,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE;QAC1B,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;KACtB;IAED,IAAI,MAAM,KAAK,IAAI,IAAI,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;QAC7C,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,EAAE;YAC3B,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;SACrB;QAED,IAAI,OAAO,CAAC,yBAAyB,KAAK,KAAK,EAAE;YAC/C,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;SACtB;QAED,yBAAyB;QACzB,gEAAgE;QAChE,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,UAAU,CAAC,CAAA;KAClC;IAED,IAAI,OAAO,CAAC,MAAM,IAAI,IAAI,EAAE;QAC1B,IAAI,OAAO,CAAC,MAAM,KAAK,SAAS,EAAE;YAChC,IAAI,CAAC,IAAI,CAAC,OAAO,OAAO,CAAC,MAAM,EAAE,CAAC,CAAA;SACnC;KACF;SAAM,IAAI,KAAK,IAAI,SAAS,EAAE;QAC7B,IAAI,CAAC,IAAI,CAAC,OAAO,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC,CAAA;KACnD;IAED,IAAI,KAAK,EAAE;QACT,kEAAkE;QAClE,mGAAmG;QACnG,2DAA2D;QAC3D,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;KAClB;IACD,OAAO,IAAI,CAAA;AACb,CAAC;AAjED,sDAiEC;AAED,6CAA6C;AAC7C,gBAAgB;AACT,KAAK,UAAU,OAAO,CAAC,MAAc,EAAE,OAAe,EAAE,YAAoB,EAAE,UAA0B,EAAE;IAC/G,MAAM,IAAI,GAAG,qBAAqB,CAAC,MAAM,EAAE,OAAO,CAAC,CAAA;IACnD,6DAA6D;IAC7D,MAAM,mBAAc,CAAC,OAAO,CAAC,CAAA;IAE7B,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC,CAAA;IAC1E,IAAI,OAAO,CAAC,QAAQ,IAAI,IAAI,EAAE;QAC5B,KAAK,MAAM,IAAI,IAAI,OAAO,CAAC,QAAQ,EAAE;YACnC,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,EAAE,CAAC,CAAA;SACzB;KACF;IAED,IAAI;QACF,MAAM,mBAAI,CACR,mBAAO,EACP,IAAI,EACJ;YACE,GAAG,EAAE,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC;SACpE,EACD,sBAAO,CAAC,OAAO,CAChB,CAAA;KACF;IAAC,OAAO,CAAC,EAAE;QACV,IAAI,CAAC,CAAC,IAAI,KAAK,QAAQ,IAAI,CAAC,CAAC,MAAM,WAAM,CAAC,YAAY,CAAC,CAAC,EAAE;YACxD,MAAM,IAAI,KAAK,CAAC,2BAA2B,YAAY,iBAAiB,CAAC,CAAA;SAC1E;aAAM;YACL,MAAM,CAAC,CAAA;SACR;KACF;IAED,OAAO,OAAO,CAAA;AAChB,CAAC;AA9BD,0BA8BC;AAED,SAAS,WAAW,CAAC,OAAkB;IACrC,MAAM,IAAI,GAAG,CAAC,OAAO,EAAE,KAAK,CAAC,CAAA;IAC7B,IAAI,sBAAO,CAAC,OAAO,EAAE;QACnB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;KACjB;IACD,OAAO,IAAI,CAAA;AACb,CAAC", "sourcesContent": ["import { path7za } from \"7zip-bin\"\nimport { debug7z, exec } from \"builder-util\"\nimport { exists, unlinkIfExists } from \"builder-util/out/fs\"\nimport { move } from \"fs-extra\"\nimport * as path from \"path\"\nimport { create, CreateOptions, FileOptions } from \"tar\"\nimport { TmpDir } from \"temp-file\"\nimport { CompressionLevel } from \"../core\"\nimport { getLinuxToolsPath } from \"./tools\"\n\n/** @internal */\nexport async function tar(\n  compression: CompressionLevel | any | any,\n  format: string,\n  outFile: string,\n  dirToArchive: string,\n  isMacApp: boolean,\n  tempDirManager: TmpDir\n): Promise<void> {\n  const tarFile = await tempDirManager.getTempFile({ suffix: \".tar\" })\n  const tarArgs: CreateOptions & FileOptions = {\n    file: tarFile,\n    portable: true,\n    cwd: dirToArchive,\n    prefix: path.basename(outFile, `.${format}`),\n  }\n  let tarDirectory = \".\"\n  if (isMacApp) {\n    delete tarArgs.prefix\n    tarArgs.cwd = path.dirname(dirToArchive)\n    tarDirectory = path.basename(dirToArchive)\n  }\n\n  await Promise.all([\n    create(tarArgs, [tarDirectory]),\n    // remove file before - 7z doesn't overwrite file, but update\n    unlinkIfExists(outFile),\n  ])\n\n  if (format === \"tar.lz\") {\n    // noinspection SpellCheckingInspection\n    let lzipPath = \"lzip\"\n    if (process.platform === \"darwin\") {\n      lzipPath = path.join(await getLinuxToolsPath(), \"bin\", lzipPath)\n    }\n    await exec(lzipPath, [compression === \"store\" ? \"-1\" : \"-9\", \"--keep\" /* keep (don't delete) input files */, tarFile])\n    // bloody lzip creates file in the same dir where input file with postfix `.lz`, option --output doesn't work\n    await move(`${tarFile}.lz`, outFile)\n    return\n  }\n\n  const args = compute7zCompressArgs(format === \"tar.xz\" ? \"xz\" : format === \"tar.bz2\" ? \"bzip2\" : \"gzip\", {\n    isRegularFile: true,\n    method: \"DEFAULT\",\n    compression,\n  })\n  args.push(outFile, tarFile)\n  await exec(\n    path7za,\n    args,\n    {\n      cwd: path.dirname(dirToArchive),\n    },\n    debug7z.enabled\n  )\n}\n\nexport interface ArchiveOptions {\n  compression?: CompressionLevel | null\n\n  /**\n   * @default false\n   */\n  withoutDir?: boolean\n\n  /**\n   * @default true\n   */\n  solid?: boolean\n\n  /**\n   * @default true\n   */\n  isArchiveHeaderCompressed?: boolean\n\n  dictSize?: number\n  excluded?: Array<string> | null\n\n  // DEFAULT allows to disable custom logic and do not pass method switch at all\n  method?: \"Copy\" | \"LZMA\" | \"Deflate\" | \"DEFAULT\"\n\n  isRegularFile?: boolean\n}\n\nexport function compute7zCompressArgs(format: string, options: ArchiveOptions = {}) {\n  let storeOnly = options.compression === \"store\"\n  const args = debug7zArgs(\"a\")\n\n  let isLevelSet = false\n  if (process.env.ELECTRON_BUILDER_COMPRESSION_LEVEL != null) {\n    storeOnly = false\n    args.push(`-mx=${process.env.ELECTRON_BUILDER_COMPRESSION_LEVEL}`)\n    isLevelSet = true\n  }\n\n  const isZip = format === \"zip\"\n  if (!storeOnly) {\n    if (isZip && options.compression === \"maximum\") {\n      // http://superuser.com/a/742034\n      args.push(\"-mfb=258\", \"-mpass=15\")\n    }\n\n    if (!isLevelSet) {\n      // https://github.com/electron-userland/electron-builder/pull/3032\n      args.push(\"-mx=\" + (!isZip || options.compression === \"maximum\" ? \"9\" : \"7\"))\n    }\n  }\n\n  if (options.dictSize != null) {\n    args.push(`-md=${options.dictSize}m`)\n  }\n\n  // https://sevenzip.osdn.jp/chm/cmdline/switches/method.htm#7Z\n  // https://stackoverflow.com/questions/27136783/7zip-produces-different-output-from-identical-input\n  // tc and ta are off by default, but to be sure, we explicitly set it to off\n  // disable \"Stores NTFS timestamps for files: Modification time, Creation time, Last access time.\" to produce the same archive for the same data\n  if (!options.isRegularFile) {\n    args.push(\"-mtc=off\")\n  }\n\n  if (format === \"7z\" || format.endsWith(\".7z\")) {\n    if (options.solid === false) {\n      args.push(\"-ms=off\")\n    }\n\n    if (options.isArchiveHeaderCompressed === false) {\n      args.push(\"-mhc=off\")\n    }\n\n    // args valid only for 7z\n    // -mtm=off disable \"Stores last Modified timestamps for files.\"\n    args.push(\"-mtm=off\", \"-mta=off\")\n  }\n\n  if (options.method != null) {\n    if (options.method !== \"DEFAULT\") {\n      args.push(`-mm=${options.method}`)\n    }\n  } else if (isZip || storeOnly) {\n    args.push(`-mm=${storeOnly ? \"Copy\" : \"Deflate\"}`)\n  }\n\n  if (isZip) {\n    // -mcu switch:  7-Zip uses UTF-8, if there are non-ASCII symbols.\n    // because default mode: 7-Zip uses UTF-8, if the local code page doesn't contain required symbols.\n    // but archive should be the same regardless where produced\n    args.push(\"-mcu\")\n  }\n  return args\n}\n\n// 7z is very fast, so, use ultra compression\n/** @internal */\nexport async function archive(format: string, outFile: string, dirToArchive: string, options: ArchiveOptions = {}): Promise<string> {\n  const args = compute7zCompressArgs(format, options)\n  // remove file before - 7z doesn't overwrite file, but update\n  await unlinkIfExists(outFile)\n\n  args.push(outFile, options.withoutDir ? \".\" : path.basename(dirToArchive))\n  if (options.excluded != null) {\n    for (const mask of options.excluded) {\n      args.push(`-xr!${mask}`)\n    }\n  }\n\n  try {\n    await exec(\n      path7za,\n      args,\n      {\n        cwd: options.withoutDir ? dirToArchive : path.dirname(dirToArchive),\n      },\n      debug7z.enabled\n    )\n  } catch (e) {\n    if (e.code === \"ENOENT\" && !(await exists(dirToArchive))) {\n      throw new Error(`Cannot create archive: \"${dirToArchive}\" doesn't exist`)\n    } else {\n      throw e\n    }\n  }\n\n  return outFile\n}\n\nfunction debug7zArgs(command: \"a\" | \"x\"): Array<string> {\n  const args = [command, \"-bd\"]\n  if (debug7z.enabled) {\n    args.push(\"-bb\")\n  }\n  return args\n}\n"]}