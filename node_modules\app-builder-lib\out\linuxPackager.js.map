{"version": 3, "file": "linuxPackager.js", "sourceRoot": "", "sources": ["../src/linuxPackager.ts"], "names": [], "mappings": ";;;AAAA,+CAA0D;AAC1D,iCAA4E;AAG5E,yDAAqD;AACrD,iEAA6D;AAI7D,mEAA+D;AAE/D,2DAA4D;AAC5D,8CAAkD;AAElD,MAAa,aAAc,SAAQ,mCAAoC;IAGrE,YAAY,IAAc;QACxB,KAAK,CAAC,IAAI,EAAE,eAAQ,CAAC,KAAK,CAAC,CAAA;QAE3B,MAAM,cAAc,GAAG,IAAI,CAAC,4BAA4B,CAAC,cAAc,CAAA;QACvE,IAAI,CAAC,cAAc,GAAG,cAAc,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,2BAAgB,CAAC,cAAc,CAAC,CAAA;IAC5H,CAAC;IAED,IAAI,aAAa;QACf,OAAO,CAAC,MAAM,EAAE,UAAU,CAAC,CAAA;IAC7B,CAAC;IAED,aAAa,CAAC,OAAsB,EAAE,MAAmE;QACvG,IAAI,MAAgC,CAAA;QACpC,MAAM,SAAS,GAAG,GAAG,EAAE;YACrB,IAAI,MAAM,IAAI,IAAI,EAAE;gBAClB,MAAM,GAAG,IAAI,qCAAiB,CAAC,IAAI,CAAC,CAAA;aACrC;YACD,OAAO,MAAM,CAAA;QACf,CAAC,CAAA;QAED,IAAI,aAAa,GAAyB,IAAI,CAAA;QAE9C,KAAK,MAAM,IAAI,IAAI,OAAO,EAAE;YAC1B,IAAI,IAAI,KAAK,iBAAU,EAAE;gBACvB,SAAQ;aACT;YAED,MAAM,WAAW,GAA+F,CAAC,GAAG,EAAE;gBACpH,QAAQ,IAAI,EAAE;oBACZ,KAAK,UAAU;wBACb,OAAO,OAAO,CAAC,0BAA0B,CAAC,CAAC,OAAO,CAAA;oBACpD,KAAK,MAAM;wBACT,OAAO,OAAO,CAAC,gBAAgB,CAAC,CAAC,OAAO,CAAA;oBAC1C,KAAK,SAAS;wBACZ,OAAO,OAAO,CAAC,yBAAyB,CAAC,CAAC,OAAO,CAAA;oBACnD,KAAK,KAAK,CAAC;oBACX,KAAK,KAAK,CAAC;oBACX,KAAK,IAAI,CAAC;oBACV,KAAK,SAAS,CAAC;oBACf,KAAK,QAAQ,CAAC;oBACd,KAAK,KAAK,CAAC;oBACX,KAAK,KAAK;wBACR,OAAO,OAAO,CAAC,eAAe,CAAC,CAAC,OAAO,CAAA;oBACzC;wBACE,OAAO,IAAI,CAAA;iBACd;YACH,CAAC,CAAC,EAAE,CAAA;YAEJ,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE;gBACpB,IAAI,WAAW,KAAK,IAAI,EAAE;oBACxB,OAAO,kCAAkB,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,CAAA;iBAC9C;gBAED,MAAM,MAAM,GAAG,IAAI,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,EAAE,MAAM,CAAC,CAAA;gBAC/D,IAAI,OAAO,CAAC,QAAQ,KAAK,OAAO,IAAI,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE;oBAC7D,IAAI,aAAa,IAAI,IAAI,EAAE;wBACzB,aAAa,GAAG,IAAI,6BAAa,CAAC,IAAI,CAAC,CAAA;qBACxC;oBACD,yEAAyE;oBACzE,OAAO,IAAI,YAAY,CAAC,MAAM,EAAE,aAAa,CAAC,CAAA;iBAC/C;gBACD,OAAO,MAAM,CAAA;YACf,CAAC,CAAC,CAAA;SACH;IACH,CAAC;CACF;AApED,sCAoEC;AAED,MAAM,YAAa,SAAQ,aAAM;IAW/B,YAA6B,MAAc,EAAmB,aAA4B;QACxF,KAAK,CACH,MAAM,CAAC,IAAI,EACX,IAAI,CAAC,4KAA4K,CAClL,CAAA;QAJ0B,WAAM,GAAN,MAAM,CAAQ;QAAmB,kBAAa,GAAb,aAAa,CAAe;QAVlF,qBAAgB,GAAG,IAAI,+BAAgB,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAA;IAenG,CAAC;IAbD,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAA;IAC5B,CAAC;IAED,IAAI,MAAM;QACR,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAA;IAC3B,CAAC;IASD,KAAK,CAAC,WAAW;QACf,MAAM,IAAI,CAAC,gBAAgB,CAAC,UAAU,EAAE,CAAA;QACxC,MAAM,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAA;IAClC,CAAC;IAED,KAAK,CAAC,SAAiB,EAAE,IAAU;QACjC,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,IAAI,CAAC,CAAA;QAC7C,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,OAAO,CAAC,CAAA;QACtC,OAAO,OAAO,CAAA;IAChB,CAAC;IAEO,KAAK,CAAC,OAAO,CAAC,SAAiB,EAAE,IAAU;QACjD,kBAAG,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,EAAE,mBAAI,CAAC,IAAI,CAAC,EAAE,EAAE,yBAAyB,CAAC,CAAA;QACnF,MAAM,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,CAAA;QAChC,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,EAAE,SAAS,CAAC,CAAA;IAChE,CAAC;CACF;AAED,SAAgB,oBAAoB,CAAC,IAAU;IAC7C,QAAQ,IAAI,EAAE;QACZ,KAAK,mBAAI,CAAC,GAAG;YACX,OAAO,QAAQ,CAAA;QACjB,KAAK,mBAAI,CAAC,IAAI;YACZ,OAAO,MAAM,CAAA;QACf,KAAK,mBAAI,CAAC,MAAM;YACd,OAAO,KAAK,CAAA;QACd,KAAK,mBAAI,CAAC,KAAK;YACb,OAAO,aAAa,CAAA;QAEtB;YACE,MAAM,IAAI,KAAK,CAAC,oBAAoB,IAAI,EAAE,CAAC,CAAA;KAC9C;AACH,CAAC;AAdD,oDAcC", "sourcesContent": ["import { Arch, AsyncTaskManager, log } from \"builder-util\"\nimport { DIR_TARGET, Platform, Target, TargetSpecificOptions } from \"./core\"\nimport { LinuxConfiguration } from \"./options/linuxOptions\"\nimport { Packager } from \"./packager\"\nimport { PlatformPackager } from \"./platformPackager\"\nimport { RemoteBuilder } from \"./remoteBuilder/RemoteBuilder\"\nimport AppImageTarget from \"./targets/AppImageTarget\"\nimport FlatpakTarget from \"./targets/FlatpakTarget\"\nimport FpmTarget from \"./targets/fpm\"\nimport { LinuxTargetHelper } from \"./targets/LinuxTargetHelper\"\nimport SnapTarget from \"./targets/snap\"\nimport { createCommonTarget } from \"./targets/targetFactory\"\nimport { sanitizeFileName } from \"./util/filename\"\n\nexport class LinuxPackager extends PlatformPackager<LinuxConfiguration> {\n  readonly executableName: string\n\n  constructor(info: Packager) {\n    super(info, Platform.LINUX)\n\n    const executableName = this.platformSpecificBuildOptions.executableName\n    this.executableName = executableName == null ? this.appInfo.sanitizedName.toLowerCase() : sanitizeFileName(executableName)\n  }\n\n  get defaultTarget(): Array<string> {\n    return [\"snap\", \"appimage\"]\n  }\n\n  createTargets(targets: Array<string>, mapper: (name: string, factory: (outDir: string) => Target) => void): void {\n    let helper: LinuxTargetHelper | null\n    const getHelper = () => {\n      if (helper == null) {\n        helper = new LinuxTargetHelper(this)\n      }\n      return helper\n    }\n\n    let remoteBuilder: RemoteBuilder | null = null\n\n    for (const name of targets) {\n      if (name === DIR_TARGET) {\n        continue\n      }\n\n      const targetClass: typeof AppImageTarget | typeof SnapTarget | typeof FlatpakTarget | typeof FpmTarget | null = (() => {\n        switch (name) {\n          case \"appimage\":\n            return require(\"./targets/AppImageTarget\").default\n          case \"snap\":\n            return require(\"./targets/snap\").default\n          case \"flatpak\":\n            return require(\"./targets/FlatpakTarget\").default\n          case \"deb\":\n          case \"rpm\":\n          case \"sh\":\n          case \"freebsd\":\n          case \"pacman\":\n          case \"apk\":\n          case \"p5p\":\n            return require(\"./targets/fpm\").default\n          default:\n            return null\n        }\n      })()\n\n      mapper(name, outDir => {\n        if (targetClass === null) {\n          return createCommonTarget(name, outDir, this)\n        }\n\n        const target = new targetClass(name, this, getHelper(), outDir)\n        if (process.platform === \"win32\" || process.env._REMOTE_BUILD) {\n          if (remoteBuilder == null) {\n            remoteBuilder = new RemoteBuilder(this)\n          }\n          // return remoteBuilder.buildTarget(this, arch, appOutDir, this.packager)\n          return new RemoteTarget(target, remoteBuilder)\n        }\n        return target\n      })\n    }\n  }\n}\n\nclass RemoteTarget extends Target {\n  private buildTaskManager = new AsyncTaskManager(this.remoteBuilder.packager.info.cancellationToken)\n\n  get options(): TargetSpecificOptions | null | undefined {\n    return this.target.options\n  }\n\n  get outDir(): string {\n    return this.target.outDir\n  }\n\n  constructor(private readonly target: Target, private readonly remoteBuilder: RemoteBuilder) {\n    super(\n      target.name,\n      true /* all must be scheduled in time (so, on finishBuild RemoteBuilder will have all targets added - so, we must set isAsyncSupported to true (resolved promise is returned)) */\n    )\n  }\n\n  async finishBuild() {\n    await this.buildTaskManager.awaitTasks()\n    await this.remoteBuilder.build()\n  }\n\n  build(appOutDir: string, arch: Arch) {\n    const promise = this.doBuild(appOutDir, arch)\n    this.buildTaskManager.addTask(promise)\n    return promise\n  }\n\n  private async doBuild(appOutDir: string, arch: Arch) {\n    log.info({ target: this.target.name, arch: Arch[arch] }, \"scheduling remote build\")\n    await this.target.checkOptions()\n    this.remoteBuilder.scheduleBuild(this.target, arch, appOutDir)\n  }\n}\n\nexport function toAppImageOrSnapArch(arch: Arch): string {\n  switch (arch) {\n    case Arch.x64:\n      return \"x86_64\"\n    case Arch.ia32:\n      return \"i386\"\n    case Arch.armv7l:\n      return \"arm\"\n    case Arch.arm64:\n      return \"arm_aarch64\"\n\n    default:\n      throw new Error(`Unsupported arch ${arch}`)\n  }\n}\n"]}