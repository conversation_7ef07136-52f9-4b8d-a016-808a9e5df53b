<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>Voice Widget</title>
  <link rel="stylesheet" href="index.css">
</head>
<body>
  <div class="widget-container" id="widgetContainer">
    <!-- Collapsed state - minimal floating widget -->
    <div class="widget-collapsed" id="collapsedWidget">
      <button id="recordBtn" class="icon-btn record-btn" title="Record Voice">
        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="M12 2a3 3 0 0 0-3 3v7a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3z"></path>
          <path d="M19 10v2a7 7 0 0 1-14 0v-2"></path>
          <line x1="12" y1="19" x2="12" y2="22"></line>
        </svg>
      </button>
      <!-- <button id="expandBtn" class="icon-btn expand-btn" title="Expand">
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <polyline points="15,3 21,3 21,9"></polyline>
          <polyline points="9,21 3,21 3,15"></polyline>
          <line x1="21" y1="3" x2="14" y2="10"></line>
          <line x1="3" y1="21" x2="10" y2="14"></line>
        </svg>
      </button> -->
      <button id="settingsBtn" class="icon-btn settings-btn" title="Settings">
        <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <circle cx="12" cy="12" r="3"></circle>
          <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path>
        </svg>
      </button>
      <button id="closeBtn" class="icon-btn close-btn" title="Close">
        <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <line x1="18" y1="6" x2="6" y2="18"></line>
          <line x1="6" y1="6" x2="18" y2="18"></line>
        </svg>
      </button>
    </div>

    <!-- Expanded state - shows text output -->
    <div class="widget-expanded" id="expandedWidget" style="display: none;">
      <div class="drag-handle" id="dragHandle">
        <div class="drag-dots">⋮⋮</div>
        <button id="collapseBtn" class="icon-btn collapse-btn" title="Collapse">
          <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <polyline points="4,14 10,14 10,20"></polyline>
            <polyline points="20,10 14,10 14,4"></polyline>
            <line x1="14" y1="10" x2="21" y2="3"></line>
            <line x1="3" y1="21" x2="10" y2="14"></line>
          </svg>
        </button>
      </div>
      <div class="text-area">
        <textarea id="textOutput" placeholder="Transcribed text will appear here..." spellcheck="false" readonly></textarea>
      </div>
      <div class="action-buttons">
        <button id="copyBtn" class="icon-btn copy-btn" title="Copy Text">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
            <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
          </svg>
        </button>
        <button id="clearBtn" class="icon-btn clear-btn" title="Clear Text">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M3 6h18"></path>
            <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
            <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
          </svg>
        </button>
      </div>
    </div>

    <!-- Settings Modal -->
    <div class="settings-modal" id="settingsModal" style="display: none;">
      <div class="settings-content">
        <div class="settings-header">
          <h3>Settings</h3>
          <button id="closeSettingsBtn" class="icon-btn close-settings-btn" title="Close Settings">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </button>
        </div>
        <div class="settings-body">
          <div class="setting-item">
            <label for="shortcutInput">Global Shortcut:</label>
            <div class="shortcut-input-container">
              <input type="text" id="shortcutInput" class="shortcut-input" placeholder="Press keys..." readonly>
              <button id="recordShortcutBtn" class="btn-secondary">Record</button>
            </div>
            <div class="shortcut-help">
              <small>Current: <span id="currentShortcut">Ctrl+Shift+Space</span></small>
            </div>
          </div>
        </div>
        <div class="settings-footer">
          <button id="saveSettingsBtn" class="btn-primary">Save</button>
          <button id="cancelSettingsBtn" class="btn-secondary">Cancel</button>
        </div>
      </div>
    </div>

    <!-- Status indicator -->
    <div class="status-indicator" id="statusIndicator"></div>
  </div>
  <script src="index.js"></script>
</body>
</html>