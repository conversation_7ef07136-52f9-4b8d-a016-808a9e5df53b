"use strict";
// Minimal Voice Widget Script
console.log("Voice Widget initialized");
// DOM Elements
const widgetContainer = document.getElementById('widgetContainer');
const collapsedWidget = document.getElementById('collapsedWidget');
const expandedWidget = document.getElementById('expandedWidget');
const recordBtn = document.getElementById('recordBtn');
const expandBtn = document.getElementById('expandBtn');
const collapseBtn = document.getElementById('collapseBtn');
const copyBtn = document.getElementById('copyBtn');
const clearBtn = document.getElementById('clearBtn');
const closeBtn = document.getElementById('closeBtn');
const settingsBtn = document.getElementById('settingsBtn');
const textOutput = document.getElementById('textOutput');
const statusIndicator = document.getElementById('statusIndicator');
const dragHandle = document.getElementById('dragHandle');
// Settings modal elements
const settingsModal = document.getElementById('settingsModal');
const closeSettingsBtn = document.getElementById('closeSettingsBtn');
const shortcutInput = document.getElementById('shortcutInput');
const recordShortcutBtn = document.getElementById('recordShortcutBtn');
const currentShortcut = document.getElementById('currentShortcut');
const saveSettingsBtn = document.getElementById('saveSettingsBtn');
const cancelSettingsBtn = document.getElementById('cancelSettingsBtn');
// Variables
let mediaRecorder = null;
let audioChunks = [];
let isRecording = false;
let isExpanded = false;
let shortcutPressed = false;
let buttonPressed = false;
let keyUpTimeout = null;
// Settings variables
let isRecordingShortcut = false;
let currentShortcutKeys = [];
let tempShortcut = '';
let originalShortcut = 'Control+Shift+Space';
// Initialize UI
updateStatusIndicator('ready');
// // Event listeners
// recordBtn.addEventListener('mousedown', startRecordingFromButton);
// recordBtn.addEventListener('mouseup', stopRecordingFromButton);
// recordBtn.addEventListener('mouseleave', stopRecordingFromButton);
// Stop if mouse leaves button while pressed
expandBtn.addEventListener('click', expandWidget);
collapseBtn.addEventListener('click', collapseWidget);
copyBtn.addEventListener('click', copyToClipboard);
clearBtn.addEventListener('click', clearText);
settingsBtn.addEventListener('click', openSettings);
closeBtn.addEventListener('click', () => {
    if (window.electronAPI && window.electronAPI.hideWidget) {
        window.electronAPI.hideWidget();
    }
});
// Settings event listeners
closeSettingsBtn.addEventListener('click', closeSettings);
cancelSettingsBtn.addEventListener('click', closeSettings);
saveSettingsBtn.addEventListener('click', saveSettings);
recordShortcutBtn.addEventListener('click', toggleShortcutRecording);
// Shortcut recording event listeners
shortcutInput.addEventListener('keydown', handleShortcutKeyDown);
shortcutInput.addEventListener('keyup', handleShortcutKeyUp);
// Note: Global keyboard shortcut is handled by main process, not renderer
// IPC listeners for shortcut-triggered recording from main process
if (window.electronAPI) {
    window.electronAPI.onStartRecordingFromShortcut(() => {
        if (!isRecording) {
            shortcutPressed = true;
            startRecording();
            console.log('Recording started from global shortcut');
        }
    });
    window.electronAPI.onStopRecordingFromShortcut(() => {
        if (isRecording && shortcutPressed) {
            shortcutPressed = false;
            stopRecording();
            console.log('Recording stopped from global shortcut');
        }
    });
}
// Widget state management
function expandWidget() {
    isExpanded = true;
    collapsedWidget.style.display = 'none';
    expandedWidget.style.display = 'flex';
    // Resize window for expanded state
    if (window.electronAPI && window.electronAPI.resizeWindow) {
        window.electronAPI.resizeWindow(300, 200);
    }
}
function collapseWidget() {
    isExpanded = false;
    expandedWidget.style.display = 'none';
    collapsedWidget.style.display = 'flex';
    // Resize window for collapsed state
    if (window.electronAPI && window.electronAPI.resizeWindow) {
        window.electronAPI.resizeWindow(200, 60);
    }
}
// Status indicator management
function updateStatusIndicator(status) {
    statusIndicator.className = `status-indicator ${status}`;
}
// Function to copy transcribed text to clipboard
function copyToClipboard() {
    const text = textOutput.value;
    if (!text) {
        return;
    }
    try {
        navigator.clipboard.writeText(text)
            .then(() => {
            // Visual feedback
            copyBtn.style.background = '#218838';
            setTimeout(() => {
                copyBtn.style.background = '';
            }, 1000);
        })
            .catch(err => {
            console.error('Failed to copy text: ', err);
            updateStatusIndicator('error');
        });
    }
    catch (error) {
        console.error('Error copying to clipboard:', error);
        updateStatusIndicator('error');
    }
}
// Function to clear text
function clearText() {
    textOutput.value = '';
    updateStatusIndicator('ready');
}
// Note: toggleRecording function removed - now using push-to-talk for button and keyboard shortcut
// Start recording function
async function startRecording() {
    try {
        // Request microphone access
        const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
        // Update UI
        updateStatusIndicator('recording');
        recordBtn.classList.add('recording');
        isRecording = true;
        // Keep widget collapsed - no auto-expansion needed
        // Create media recorder
        mediaRecorder = new MediaRecorder(stream);
        audioChunks = [];
        // Event handlers
        mediaRecorder.ondataavailable = (event) => {
            if (event.data.size > 0) {
                audioChunks.push(event.data);
                console.log(`Data available: ${event.data.size} bytes`);
            }
        };
        mediaRecorder.onstop = () => {
            // Create blob
            const audioBlob = new Blob(audioChunks, { type: 'audio/webm' });
            // Update UI
            updateStatusIndicator('processing');
            // Stop all tracks
            stream.getTracks().forEach(track => track.stop());
            // Try to send to main process if API is available
            if (window.electronAPI && window.electronAPI.transcribeAudio) {
                // Convert Blob to ArrayBuffer and then to Base64 string for IPC
                const reader = new FileReader();
                reader.onload = async () => {
                    try {
                        // Convert ArrayBuffer to Base64 string
                        const base64data = reader.result;
                        // Send the Base64 string to the main process
                        const response = await window.electronAPI.transcribeAudio({
                            base64Audio: base64data.split(',')[1], // Remove the data URL prefix
                            mimeType: audioBlob.type,
                            size: audioBlob.size
                        });
                        // Handle the response - transcription is automatically copied to clipboard in main process
                        if (typeof response === 'string') {
                            // Legacy format (string) - just update status
                            updateStatusIndicator('ready');
                        }
                        else if (response && typeof response === 'object') {
                            // New format (object with transcribedText and additionalInfo)
                            const { additionalInfo } = response;
                            // Update status based on response
                            if (additionalInfo && additionalInfo.error) {
                                updateStatusIndicator('error');
                            }
                            else {
                                updateStatusIndicator('ready');
                            }
                        }
                        else {
                            // Unexpected response format
                            updateStatusIndicator('error');
                        }
                        // Notify main process that recording is completed for auto-close
                        if (window.electronAPI && window.electronAPI.recordingCompleted) {
                            window.electronAPI.recordingCompleted();
                        }
                    }
                    catch (error) {
                        console.error('Error sending audio to main process:', error);
                        updateStatusIndicator('error');
                    }
                };
                reader.onerror = () => {
                    const error = reader.error;
                    console.error('Error reading audio blob:', error);
                    updateStatusIndicator('error');
                };
                // Start reading the Blob as Data URL (base64)
                reader.readAsDataURL(audioBlob);
            }
        };
        // Start recording
        mediaRecorder.start(1000); // Collect data every second
        console.log('Recording started');
    }
    catch (error) {
        console.error('Error starting recording:', error);
        updateStatusIndicator('error');
        recordBtn.classList.remove('recording');
        isRecording = false;
    }
}
// Stop recording function
function stopRecording() {
    if (mediaRecorder && isRecording) {
        mediaRecorder.stop();
        recordBtn.classList.remove('recording');
        isRecording = false;
        updateStatusIndicator('processing');
        console.log('Recording stopped');
    }
}
// Push-to-talk button functions
async function startRecordingFromButton(event) {
    // Prevent default behavior and ensure we only respond to left mouse button
    if (event.button !== 0)
        return;
    event.preventDefault();
    // Don't start if already recording from keyboard shortcut
    if (isRecording && shortcutPressed)
        return;
    // Don't start if already recording from button
    if (buttonPressed)
        return;
    buttonPressed = true;
    console.log('Recording started from button press');
    // Start recording if not already recording
    if (!isRecording) {
        await startRecording();
    }
}
function stopRecordingFromButton(event) {
    // Only stop if we started recording from button press
    if (!buttonPressed)
        return;
    event.preventDefault();
    buttonPressed = false;
    console.log('Recording stopped from button release');
    // Stop recording if we're currently recording from button
    if (isRecording && !shortcutPressed) {
        stopRecording();
    }
}
// Settings functions
async function openSettings() {
    // Load current settings
    if (window.electronAPI && window.electronAPI.getSettings) {
        try {
            const settings = await window.electronAPI.getSettings();
            originalShortcut = settings.globalShortcut || 'Control+Shift+Space';
            currentShortcut.textContent = originalShortcut;
            shortcutInput.value = '';
            tempShortcut = '';
        }
        catch (error) {
            console.error('Error loading settings:', error);
        }
    }
    settingsModal.style.display = 'flex';
    // Resize window for settings
    if (window.electronAPI && window.electronAPI.resizeWindow) {
        window.electronAPI.resizeWindow(400, 300);
    }
}
function closeSettings() {
    settingsModal.style.display = 'none';
    isRecordingShortcut = false;
    recordShortcutBtn.textContent = 'Record';
    shortcutInput.value = '';
    tempShortcut = '';
    currentShortcutKeys = [];
    // Resize back to collapsed state
    if (window.electronAPI && window.electronAPI.resizeWindow) {
        window.electronAPI.resizeWindow(200, 60);
    }
}
async function saveSettings() {
    const shortcutToSave = tempShortcut || originalShortcut;
    if (window.electronAPI && window.electronAPI.saveSettings) {
        try {
            await window.electronAPI.saveSettings({
                globalShortcut: shortcutToSave
            });
            originalShortcut = shortcutToSave;
            currentShortcut.textContent = shortcutToSave;
            closeSettings();
        }
        catch (error) {
            console.error('Error saving settings:', error);
            alert('Error saving settings. Please try again.');
        }
    }
}
function toggleShortcutRecording() {
    if (isRecordingShortcut) {
        // Stop recording
        isRecordingShortcut = false;
        recordShortcutBtn.textContent = 'Record';
        shortcutInput.blur();
    }
    else {
        // Start recording
        isRecordingShortcut = true;
        recordShortcutBtn.textContent = 'Stop';
        shortcutInput.focus();
        shortcutInput.value = 'Press keys...';
        currentShortcutKeys = [];
        tempShortcut = '';
    }
}
function handleShortcutKeyDown(event) {
    if (!isRecordingShortcut)
        return;
    event.preventDefault();
    event.stopPropagation();
    const key = event.key;
    // Build the shortcut string
    const modifiers = [];
    if (event.ctrlKey)
        modifiers.push('Control');
    if (event.altKey)
        modifiers.push('Alt');
    if (event.shiftKey)
        modifiers.push('Shift');
    if (event.metaKey)
        modifiers.push('Meta');
    // Add the main key if it's not a modifier
    if (!['Control', 'Alt', 'Shift', 'Meta'].includes(key)) {
        let mainKey = key;
        // Convert some keys to more readable format
        if (key === ' ')
            mainKey = 'Space';
        else if (key.length === 1)
            mainKey = key.toUpperCase();
        const shortcut = [...modifiers, mainKey].join('+');
        tempShortcut = shortcut;
        shortcutInput.value = shortcut;
    }
    else {
        // Just show modifiers while they're being pressed
        shortcutInput.value = modifiers.join('+') + (modifiers.length > 0 ? '+' : '');
    }
}
function handleShortcutKeyUp(event) {
    if (!isRecordingShortcut)
        return;
    event.preventDefault();
    event.stopPropagation();
    // If we have a complete shortcut, stop recording
    if (tempShortcut && tempShortcut.includes('+') && !tempShortcut.endsWith('+')) {
        setTimeout(() => {
            isRecordingShortcut = false;
            recordShortcutBtn.textContent = 'Record';
            shortcutInput.blur();
        }, 100);
    }
}
// Note: Global keyboard handlers removed - shortcut is now handled by main process
