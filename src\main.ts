import { app, BrowserWindow, globalShortcut, ipcMain, Menu, Tray, clipboard, nativeImage } from 'electron';
import * as path from 'path';
import * as dotenv from 'dotenv';
import * as https from 'https';
import * as fs from 'fs';
import * as os from 'os';
import { exec } from 'child_process';
import FormData from 'form-data';
import Store from 'electron-store';
import keysender from 'node-key-sender';

// Add these variables
let tray: Tray | null = null;
let dashboardWindow: BrowserWindow | null = null;

// Create dashboard window function
function createDashboardWindow() {
  if (dashboardWindow) {
    dashboardWindow.focus();
    return;
  }
  
  dashboardWindow = new BrowserWindow({
    width: 800,
    height: 600,
    webPreferences: {
      preload: path.join(__dirname, 'preload.js'),
      nodeIntegration: true,
      contextIsolation: true
    }
  });
  
  dashboardWindow.loadFile(path.join(__dirname, 'renderer', 'dashboard.html'));
  
  dashboardWindow.on('closed', () => {
    dashboardWindow = null;
  });
}

// Create system tray
function createTray() {
  // Use a fallback icon if tray-icon.png doesn't exist
  let iconPath = path.join(__dirname, 'tray-icon.png');
  if (!fs.existsSync(iconPath)) {
    // Create a simple 16x16 transparent PNG as fallback
    iconPath = path.join(__dirname, 'assets', 'tray-icon.png');
    if (!fs.existsSync(iconPath)) {
      // Use nativeImage to create a simple icon
      const { nativeImage } = require('electron');
      const icon = nativeImage.createEmpty();
      tray = new Tray(icon);
    } else {
      tray = new Tray(iconPath);
    }
  } else {
    tray = new Tray(iconPath);
  }
  
  const contextMenu = Menu.buildFromTemplate([
    { 
      label: 'Settings', 
      click: () => {
        createDashboardWindow();
      }
    },
    { type: 'separator' },
    { 
      label: 'Quit', 
      click: () => {
        app.quit();
      }
    }
  ]);
  
  tray.setToolTip('Voice-to-Text Widget');
  tray.setContextMenu(contextMenu);
  
  // Double-click opens dashboard
  tray.on('double-click', () => {
    createDashboardWindow();
  });
}

dotenv.config();

const OPENAI_API_KEY = process.env.OPENAI_API_KEY;

// Initialize settings store
const store = new Store({
  defaults: {
    globalShortcut: 'Control+Shift+Space'
  }
});

let mainWindow: BrowserWindow | null = null;
let currentShortcut = store.get('globalShortcut', 'Control+Shift+Space') as string;
let isRecordingActive = false;

function createWindow() {
  const { screen } = require('electron');
  const primaryDisplay = screen.getPrimaryDisplay();
  const { width: screenWidth } = primaryDisplay.workAreaSize;

  const windowWidth = 120;
  const windowHeight = 120;

  mainWindow = new BrowserWindow({
    width: windowWidth,
    height: windowHeight,
    x: Math.round((screenWidth - windowWidth) / 2), // Center horizontally
    y: 50, // Position at top of screen
    frame: false,
    transparent: true,
    focusable: false,
    alwaysOnTop: true,
    skipTaskbar: true,
    resizable: false,
    movable: true,
    webPreferences: {
      preload: path.join(__dirname, 'preload.js'),
      nodeIntegration: true,
      contextIsolation: true
    }
  });

  // Load the widget HTML file
  mainWindow.loadFile(path.join(__dirname, 'renderer', 'widget.html'));

  // Hide by default until shortcut is pressed
  mainWindow.hide();
}

function handleShortcutPress() {
  if (!mainWindow) return;

  // Show the widget and start recording immediately
  mainWindow.show();
  isRecordingActive = true;

  // Notify widget to show recording animation
  mainWindow.webContents.send('recording-started');

  // Start recording
  mainWindow.webContents.send('start-recording-from-shortcut');
}

function handleShortcutRelease() {
  if (!mainWindow || !isRecordingActive) return;
  
  isRecordingActive = false;
  
  // Notify widget to hide recording animation
  mainWindow.webContents.send('recording-stopped');
  
  // Stop recording and process transcription
  mainWindow.webContents.send('stop-recording-from-shortcut');
}

async function transcribeAudio(audioBuffer: Buffer): Promise<string> {
  return new Promise((resolve, reject) => {
    try {
      if (!OPENAI_API_KEY) {
        throw new Error('OpenAI API key is not set in .env file');
      }

      console.log('Audio buffer size:', audioBuffer.length);

      if (audioBuffer.length === 0) {
        throw new Error('Empty audio buffer received');
      }

      // Create a temporary file to store the audio
      const tempFilePath = path.join(os.tmpdir(), `audio-${Date.now()}.wav`);
      console.log('Writing audio to temporary file:', tempFilePath);
      fs.writeFileSync(tempFilePath, audioBuffer);

      // Verify the file was written
      const fileStats = fs.statSync(tempFilePath);
      console.log('Temporary file size:', fileStats.size);

      if (fileStats.size === 0) {
        throw new Error('Failed to write audio data to temporary file');
      }

      // Create form data
      const form = new FormData();
      form.append('file', fs.createReadStream(tempFilePath), {
        filename: 'audio.wav',
        contentType: 'audio/wav'
      });
      form.append('model', 'whisper-1');
      form.append('response_format', 'text');

      console.log('Form data created, sending to OpenAI API...');

      // Set up the request options
      const options = {
        hostname: 'api.openai.com',
        path: '/v1/audio/transcriptions',
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${OPENAI_API_KEY}`,
          ...form.getHeaders()
        }
      };

      console.log('Making request to OpenAI API...');

      // Make the request
      const req = https.request(options, (res) => {
        let data = '';

        res.on('data', (chunk) => {
          data += chunk;
        });

        res.on('end', () => {
          console.log('Response received, status code:', res.statusCode);

          // Clean up the temporary file
          try {
            fs.unlinkSync(tempFilePath);
            console.log('Temporary file deleted');
          } catch (err) {
            console.error('Error deleting temporary file:', err);
          }

          if (res.statusCode !== 200) {
            console.error('OpenAI API error:', res.statusCode, data);
            reject(new Error(`OpenAI API error: ${res.statusCode} ${data}`));
            return;
          }

          console.log('Transcription successful');
          resolve(data);
        });
      });

      req.on('error', (error) => {
        console.error('Request error:', error);

        // Clean up the temporary file
        try {
          fs.unlinkSync(tempFilePath);
          console.log('Temporary file deleted');
        } catch (err) {
          console.error('Error deleting temporary file:', err);
        }

        reject(error);
      });

      // Send the form data
      form.pipe(req);
      console.log('Request sent');

    } catch (error) {
      console.error('Error transcribing audio:', error);
      reject(error);
    }
  });
}

function isEnglishText(text: string): boolean {
  // Check if text contains mostly English characters
  const englishPattern = /^[a-zA-Z0-9\s.,!?'"()-]+$/;
  const englishWords = ['the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'have', 'has', 'had', 'will', 'would', 'could', 'should', 'can', 'may', 'might', 'must', 'shall', 'do', 'does', 'did', 'get', 'got', 'go', 'went', 'come', 'came', 'see', 'saw', 'know', 'knew', 'think', 'thought', 'say', 'said', 'tell', 'told', 'give', 'gave', 'take', 'took', 'make', 'made', 'want', 'need', 'like', 'love', 'help', 'work', 'play', 'look', 'find', 'use', 'try', 'ask', 'feel', 'seem', 'become', 'leave', 'put', 'mean', 'keep', 'let', 'begin', 'start', 'show', 'hear', 'play', 'run', 'move', 'live', 'believe', 'hold', 'bring', 'happen', 'write', 'provide', 'sit', 'stand', 'lose', 'pay', 'meet', 'include', 'continue', 'set', 'learn', 'change', 'lead', 'understand', 'watch', 'follow', 'stop', 'create', 'speak', 'read', 'allow', 'add', 'spend', 'grow', 'open', 'walk', 'win', 'offer', 'remember', 'consider', 'appear', 'buy', 'wait', 'serve', 'die', 'send', 'expect', 'build', 'stay', 'fall', 'cut', 'reach', 'kill', 'remain'];

  // If text contains non-English characters, it's likely not English
  if (!englishPattern.test(text.replace(/[^\x00-\x7F]/g, ''))) {
    return false;
  }

  // Check if text contains common English words
  const words = text.toLowerCase().split(/\s+/);
  const englishWordCount = words.filter(word => englishWords.includes(word.replace(/[.,!?'"()-]/g, ''))).length;

  // If more than 20% of words are common English words, consider it English
  return englishWordCount / words.length > 0.2;
}

async function detectAndTranslate(text: string): Promise<string> {
  return new Promise((resolve, reject) => {
    try {
      // If text is already in English, return it as-is
      if (isEnglishText(text)) {
        console.log('Text detected as English, returning original text');
        resolve(text);
        return;
      }

      console.log('Text detected as non-English, translating...');

      if (!OPENAI_API_KEY) {
        throw new Error('OpenAI API key is not set in .env file');
      }

      const data = JSON.stringify({
        model: 'gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content: 'You are a literal translation assistant. Translate the given text to English word-for-word. Do not interpret, explain, answer questions, or add any extra content. Only provide the direct English translation of the exact words spoken. If someone says "I need leave tomorrow", translate it as "I need leave tomorrow" - do not change it to "I need to take leave tomorrow" or provide suggestions.'
          },
          {
            role: 'user',
            content: `Translate this text to English literally: "${text}"`
          }
        ],
        temperature: 0.0
      });

      // Set up the request options
      const options = {
        hostname: 'api.openai.com',
        path: '/v1/chat/completions',
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${OPENAI_API_KEY}`,
          'Content-Length': Buffer.byteLength(data)
        }
      };

      // Make the request
      const req = https.request(options, (res) => {
        let responseData = '';

        res.on('data', (chunk) => {
          responseData += chunk;
        });

        res.on('end', () => {
          if (res.statusCode !== 200) {
            reject(new Error(`OpenAI API error: ${res.statusCode} ${responseData}`));
            return;
          }

          try {
            const parsedData = JSON.parse(responseData);
            resolve(parsedData.choices[0].message.content.trim());
          } catch (error: any) {
            reject(new Error(`Error parsing OpenAI response: ${error.message || 'Unknown error'}`));
          }
        });
      });

      req.on('error', (error) => {
        reject(error);
      });

      // Send the data
      req.write(data);
      req.end();
    } catch (error) {
      console.error('Error detecting and translating:', error);
      reject(error);
    }
  });
}

// IPC Handlers
ipcMain.on('hide-widget', () => {
  mainWindow?.hide();
});

ipcMain.on('resize-window', (_event, width: number, height: number) => {
  if (mainWindow) {
    mainWindow.setSize(width, height);
  }
});

// New IPC handler for recording completion notification
ipcMain.on('recording-completed', () => {
  // Reset recording state when transcription is completed
  isRecordingActive = false;
  console.log('Recording and transcription completed');
});

// Define the AudioData interface
interface AudioData {
  base64Audio: string;
  mimeType: string;
  size: number;
}

ipcMain.handle('transcribe-audio', async (_event, audioData: AudioData) => {
  try {
    console.log('Received audio data for transcription:');
    console.log('- Size:', audioData.size, 'bytes');
    console.log('- MIME type:', audioData.mimeType);
    console.log('- Base64 data length:', audioData.base64Audio.length);

    // Check if the data is valid
    if (!audioData.base64Audio || audioData.base64Audio.length === 0) {
      throw new Error('Received empty audio data');
    }

    // Convert base64 to buffer
    const buffer = Buffer.from(audioData.base64Audio, 'base64');
    console.log('Converted to buffer, size:', buffer.length);

    // Use the actual OpenAI API for transcription
    console.log('Starting transcription with OpenAI API...');

    try {
      // Create a temporary file to store the audio
      const tempFilePath = path.join(os.tmpdir(), `audio-${Date.now()}.wav`);
      console.log('Writing audio to temporary file:', tempFilePath);
      fs.writeFileSync(tempFilePath, buffer);

      // Verify the file was written
      const fileStats = fs.statSync(tempFilePath);
      console.log('Temporary file size:', fileStats.size);

      // Create form data
      const form = new FormData();
      form.append('file', fs.createReadStream(tempFilePath), {
        filename: 'audio.wav',
        contentType: audioData.mimeType
      });
      form.append('model', 'whisper-1');
      form.append('response_format', 'text');

      console.log('Sending request to OpenAI API...');

      // Make the request to OpenAI API
      return new Promise((resolve) => {
        const req = https.request({
          hostname: 'api.openai.com',
          path: '/v1/audio/transcriptions',
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${OPENAI_API_KEY}`,
            ...form.getHeaders()
          }
        }, async (res) => {
          let data = '';

          res.on('data', (chunk) => {
            data += chunk;
          });

          res.on('end', async () => {
            // Clean up the temporary file
            try {
              fs.unlinkSync(tempFilePath);
              console.log('Temporary file deleted');
            } catch (err) {
              console.error('Error deleting temporary file:', err);
            }

            if (res.statusCode !== 200) {
              console.error('OpenAI API error:', res.statusCode, data);
              resolve({
                transcribedText: `Error: ${res.statusCode} - ${data}`,
                additionalInfo: {
                  error: true,
                  statusCode: res.statusCode
                }
              });
              return;
            }

            // Success - get the transcribed text
            const transcription = data.trim();
            console.log('Transcription completed:', transcription);

            // Translate to English if needed
            let finalText = transcription;
            try {
              finalText = await detectAndTranslate(transcription);
              console.log('Translation completed:', finalText);
            } catch (err) {
              console.error('Translation failed, using original text:', err);
            }

            // Copy the translated text to clipboard
            clipboard.writeText(finalText);
            console.log('Text copied to clipboard:', finalText);

            // Auto-paste the text after a short delay
            setTimeout(() => {
              console.log('Auto-pasting text to focused input field');
              // Use globalShortcut to simulate Ctrl+V system-wide
              try {
                // Temporarily register Ctrl+V to trigger paste
                const pasteShortcut = 'Control+V';
                globalShortcut.register(pasteShortcut, () => {
                  // This will be triggered when we simulate the paste
                  console.log('Paste shortcut triggered');
                });

                // Immediately unregister and trigger the paste
                setTimeout(() => {
                  globalShortcut.unregister(pasteShortcut);
                  // Send the paste command to the system
                  if (process.platform === 'win32') {
                    // On Windows, we can use the shell to send Ctrl+V
                    exec('powershell -command "Add-Type -AssemblyName System.Windows.Forms; [System.Windows.Forms.SendKeys]::SendWait(\'^v\')"', (error: any) => {
                      if (error) {
                        console.error('Error sending paste command:', error);
                      } else {
                        console.log('Paste command sent successfully');
                      }
                    });
                  }
                }, 10);
              } catch (error) {
                console.error('Error setting up auto-paste:', error);
              }
            }, 500); // Increased delay to ensure clipboard is ready



// setTimeout(() => {
//   console.log('Auto-pasting text using PowerShell SendKeys');

//   if (process.platform === 'win32') {
//     const pasteCommand = `
//       powershell -WindowStyle Hidden -Command "Add-Type -AssemblyName 'System.Windows.Forms'; 
//       [System.Windows.Forms.SendKeys]::SendWait('^v')"
//     `;

//     exec(pasteCommand, (error, stdout, stderr) => {
//       if (error) {
//         console.error('Error sending paste command:', error);
//       } else {
//         console.log('Paste command sent successfully via PowerShell');
//       }
//     });
//   } else {
//     console.warn('Auto-paste is only implemented for Windows.');
//   }
// }, 500);




            // Return both the transcribed text and additional info
        
        
            resolve({
              transcribedText: finalText,
              additionalInfo: {
                audioSize: audioData.size,
                mimeType: audioData.mimeType,
                originalText: transcription !== finalText ? transcription : undefined
              }
            });
          });
        });

        req.on('error', (error) => {
          // Clean up the temporary file
          try {
            fs.unlinkSync(tempFilePath);
            console.log('Temporary file deleted');
          } catch (err) {
            console.error('Error deleting temporary file:', err);
          }

          console.error('Request error:', error);
          resolve({
            transcribedText: `Error: ${error.message}`,
            additionalInfo: {
              error: true
            }
          });
        });

        // Send the form data
        form.pipe(req);
        console.log('Request sent to OpenAI API');
      });
    } catch (error) {
      console.error('Error in transcription process:', error);
      return {
        transcribedText: `Error: ${error instanceof Error ? error.message : String(error)}`,
        additionalInfo: {
          error: true
        }
      };
    }
  } catch (error) {
    console.error('Error handling transcribe-audio:', error);
    const errorMessage = "Error processing audio: " + (error instanceof Error ? error.message : String(error));

    return {
      transcribedText: errorMessage,
      additionalInfo: {
        error: true
      }
    };
  }
});

ipcMain.handle('detect-and-translate', async (_event, text: string) => {
  try {
    const translatedText = await detectAndTranslate(text);
    return translatedText;
  } catch (error) {
    console.error('Error handling detect-and-translate:', error);
    throw error;
  }
});

// Settings IPC handlers
ipcMain.handle('get-settings', async () => {
  return {
    globalShortcut: store.get('globalShortcut', 'Control+Shift+Space')
  };
});

ipcMain.handle('save-settings', async (_event, settings: any) => {
  try {
    const newShortcut = settings.globalShortcut;

    // Unregister current shortcut
    globalShortcut.unregisterAll();

    // Register new shortcut
    if (newShortcut) {
      const success = globalShortcut.register(newShortcut, () => {
        handleShortcutPress();
      });

      if (success) {
        // Save to store
        store.set('globalShortcut', newShortcut);
        currentShortcut = newShortcut;
        console.log('Shortcut updated to:', newShortcut);
        return { success: true };
      } else {
        // Re-register old shortcut if new one failed
        globalShortcut.register(currentShortcut, () => {
          handleShortcutPress();
        });
        throw new Error('Failed to register new shortcut. It may be in use by another application.');
      }
    }

    return { success: true };
  } catch (error) {
    console.error('Error saving settings:', error);
    throw error;
  }
});

app.whenReady().then(() => {
  createWindow();
  createTray();

  // Register the stored shortcut
  const success = globalShortcut.register(currentShortcut, () => {
    handleShortcutPress();
  });
  
  // Register shortcut release event
  if (success) {
    // We need to use a different approach for detecting key release
    // This is a simplified example - actual implementation may vary
    globalShortcut.register(currentShortcut + '+UP', () => {
      handleShortcutRelease();
    });
    
    console.log('Global shortcut registered:', currentShortcut);
  } else {
    console.error('Failed to register global shortcut:', currentShortcut);
    // Fallback to default shortcut
    globalShortcut.register('Control+Shift+Space', () => {
      handleShortcutPress();
    });
  }

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) createWindow();
  });
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') app.quit();
});

app.on('will-quit', () => {
  globalShortcut.unregisterAll();
});

// Add IPC handlers for dashboard
ipcMain.handle('get-history', async () => {
  // Return transcription history from store
  return store.get('history', []);
});

ipcMain.handle('login', async (_event, credentials) => {
  // Implement login logic here
  // This is a placeholder - you would typically call an API
  if (credentials.email && credentials.password) {
    const user = { name: credentials.email.split('@')[0], email: credentials.email };
    store.set('user', user);
    return { success: true, user };
  }
  return { success: false, message: 'Invalid credentials' };
});

ipcMain.handle('logout', async () => {
  store.delete('user' as any);
  return { success: true };
});

ipcMain.handle('check-auth', async () => {
  const user = store.get('user');
  return { authenticated: !!user, user };
});


