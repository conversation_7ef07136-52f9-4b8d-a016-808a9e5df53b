{"version": 3, "file": "winPackager.js", "sourceRoot": "", "sources": ["../src/winPackager.ts"], "names": [], "mappings": ";;;AAAA,+CAA0C;AAC1C,+CAAoG;AACpG,+DAA8C;AAC9C,4CAAgF;AAChF,mCAAmC;AACnC,0CAAqC;AACrC,8BAA6B;AAC7B,uCAA+B;AAC/B,6BAA4B;AAC5B,kDAAuD;AACvD,gEASmC;AAEnC,iCAAqD;AAGrD,yDAAoE;AAEpE,0DAAsD;AACtD,sDAA6E;AAC7E,0EAAsE;AACtE,2DAA4D;AAC5D,sDAA+D;AAC/D,wCAAkD;AAClD,wCAAmC;AACnC,gCAAiD;AACjD,iCAAiC;AAEjC,MAAa,WAAY,SAAQ,mCAAsC;IA2FrE,YAAY,IAAc;QACxB,KAAK,CAAC,IAAI,EAAE,eAAQ,CAAC,OAAO,CAAC,CAAA;QA3FtB,YAAO,GAAG,IAAI,eAAI,CAAwD,GAAG,EAAE;YACtF,MAAM,4BAA4B,GAAG,IAAI,CAAC,4BAA4B,CAAA;YACtE,IAAI,4BAA4B,CAAC,sBAAsB,IAAI,IAAI,IAAI,4BAA4B,CAAC,eAAe,IAAI,IAAI,EAAE;gBACvH,OAAO,IAAI,CAAC,EAAE,CAAC,KAAK;qBACjB,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,6CAA2B,CAAC,4BAA4B,EAAE,EAAE,CAAC,CAAC;qBACzE,KAAK,CAAC,CAAC,CAAC,EAAE;oBACT,kEAAkE;oBAClE,IAAI,4BAA4B,CAAC,IAAI,IAAI,IAAI,EAAE;wBAC7C,MAAM,CAAC,CAAA;qBACR;yBAAM;wBACL,kBAAG,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,EAAE,mCAAmC,CAAC,CAAA;wBAC5D,OAAO,IAAI,CAAA;qBACZ;gBACH,CAAC,CAAC,CAAA;aACL;YAED,MAAM,eAAe,GAAG,4BAA4B,CAAC,eAAe,CAAA;YACpE,IAAI,eAAe,IAAI,IAAI,EAAE;gBAC3B,MAAM,mBAAmB,GAAG,IAAI,CAAC,cAAc,EAAE,CAAA;gBACjD,OAAO,OAAO,CAAC,OAAO,CAAC;oBACrB,IAAI,EAAE,eAAe;oBACrB,QAAQ,EAAE,mBAAmB,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,mBAAmB,CAAC,IAAI,EAAE;iBAC1E,CAAC,CAAA;aACH;YAED,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,CAAA;YAC/C,IAAI,OAAO,IAAI,IAAI,EAAE;gBACnB,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;aAC7B;YAED,OAAO,CACL,4BAAiB,CAAC,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,UAAU,CAAC;gBACnE,cAAc;iBACb,KAAK,CAAC,CAAC,CAAC,EAAE;gBACT,IAAI,CAAC,YAAY,wCAAyB,EAAE;oBAC1C,MAAM,IAAI,wCAAyB,CAAC,oDAAoD,CAAC,CAAC,OAAO,EAAE,CAAC,CAAA;iBACrG;qBAAM;oBACL,MAAM,CAAC,CAAA;iBACR;YACH,CAAC,CAAC;iBACD,IAAI,CAAC,IAAI,CAAC,EAAE;gBACX,OAAO;oBACL,IAAI,EAAE,IAAI;oBACV,QAAQ,EAAE,IAAI,CAAC,cAAc,EAAE;iBAChC,CAAA;YACH,CAAC,CAAC,CACL,CAAA;QACH,CAAC,CAAC,CAAA;QAEM,cAAS,GAAG,IAAI,eAAI,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC,CAAA;QAEvD,OAAE,GAAG,IAAI,eAAI,CAAY,GAAG,EAAE,CAAC,CAAC,OAAO,CAAC,QAAQ,KAAK,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,cAAS,EAAE,CAAC,CAAC,CAAC,CAAC,iBAAY,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAA;QAElI,0BAAqB,GAAG,IAAI,eAAI,CAAuB,KAAK,IAAI,EAAE;YACzE,MAAM,aAAa,GAAG,IAAI,CAAC,4BAA4B,CAAC,aAAa,CAAA;YACrE,IAAI,aAAa,KAAK,IAAI,EAAE;gBAC1B,OAAO,IAAI,CAAA;aACZ;iBAAM,IAAI,aAAa,IAAI,IAAI,EAAE;gBAChC,OAAO,sBAAO,CAAC,aAAa,CAAC,CAAA;aAC9B;YAED,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,KAAK,CAAA;YAC9C,OAAO,QAAQ,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAA;QACxD,CAAC,CAAC,CAAA;QAEO,iBAAY,GAAG,IAAI,eAAI,CAAyB,KAAK,IAAI,EAAE;YAClE,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,KAAK,CAAA;YACxC,IAAI,OAAO,IAAI,IAAI,EAAE;gBACnB,OAAO,IAAI,CAAA;aACZ;YAED,IAAI,SAAS,IAAI,OAAO,EAAE;gBACxB,MAAM,wBAAwB,GAAG,OAAO,CAAC,OAAO,CAAA;gBAChD,OAAO;oBACL,UAAU,EAAE,8BAAO,CAAC,wBAAwB,CAAC,CAAC,GAAG,CAAC,IAAI,CAAE;oBACxD,wBAAwB;iBACzB,CAAA;aACF;YAED,MAAM,OAAO,GAAG,OAAO,CAAC,IAAI,CAAA;YAC5B,IAAI,OAAO,IAAI,IAAI,EAAE;gBACnB,OAAO,IAAI,CAAA;aACZ;YACD,OAAO,MAAM,6BAAW,CAAC,OAAO,EAAE,OAAO,CAAC,QAAQ,IAAI,EAAE,CAAC,CAAA;QAC3D,CAAC,CAAC,CAAA;IAQF,CAAC;IAND,IAAI,8BAA8B;QAChC,OAAO,IAAI,CAAC,4BAA4B,CAAC,yBAAyB,KAAK,KAAK,CAAA;IAC9E,CAAC;IAMD,IAAI,aAAa;QACf,OAAO,CAAC,MAAM,CAAC,CAAA;IACjB,CAAC;IAES,gBAAgB;QACxB,OAAO,gCAAa,CAAC,gCAAa,CAAC,IAAI,CAAC,4BAA4B,CAAC,mBAAmB,EAAE,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC,EAAE,KAAK,CAAC,gBAAgB,EAAE,CAAC,CAAA;IACxJ,CAAC;IAED,aAAa,CAAC,OAAsB,EAAE,MAAmE;QACvG,IAAI,iBAA2C,CAAA;QAC/C,MAAM,oBAAoB,GAAG,GAAG,EAAE;YAChC,IAAI,iBAAiB,IAAI,IAAI,EAAE;gBAC7B,iBAAiB,GAAG,IAAI,4BAAiB,EAAE,CAAA;aAC5C;YACD,OAAO,iBAAiB,CAAA;QAC1B,CAAC,CAAA;QAED,IAAI,MAA+B,CAAA;QACnC,MAAM,SAAS,GAAG,GAAG,EAAE;YACrB,IAAI,MAAM,IAAI,IAAI,EAAE;gBAClB,MAAM,GAAG,IAAI,2BAAgB,CAAC,oBAAoB,EAAE,CAAC,CAAA;aACtD;YACD,OAAO,MAAM,CAAA;QACf,CAAC,CAAA;QAED,KAAK,MAAM,IAAI,IAAI,OAAO,EAAE;YAC1B,IAAI,IAAI,KAAK,iBAAU,EAAE;gBACvB,SAAQ;aACT;YAED,IAAI,IAAI,KAAK,MAAM,IAAI,IAAI,KAAK,UAAU,EAAE;gBAC1C,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE,CAAC,IAAI,uBAAU,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC,CAAC,CAAA;aACxE;iBAAM,IAAI,IAAI,KAAK,UAAU,EAAE;gBAC9B,+CAA+C;gBAC/C,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE,CAAC,IAAI,uCAAkB,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,EAAE,IAAI,EAAE,IAAI,2BAAgB,CAAC,oBAAoB,EAAE,CAAC,CAAC,CAAC,CAAA;aAClI;iBAAM;gBACL,MAAM,WAAW,GAAiD,CAAC,GAAG,EAAE;oBACtE,QAAQ,IAAI,EAAE;wBACZ,KAAK,UAAU;4BACb,IAAI;gCACF,OAAO,OAAO,CAAC,mCAAmC,CAAC,CAAC,OAAO,CAAA;6BAC5D;4BAAC,OAAO,CAAC,EAAE;gCACV,MAAM,IAAI,wCAAyB,CAAC,qGAAqG,CAAC,CAAC,KAAK,IAAI,CAAC,EAAE,CAAC,CAAA;6BACzJ;wBAEH,KAAK,MAAM;4BACT,OAAO,OAAO,CAAC,sBAAsB,CAAC,CAAC,OAAO,CAAA;wBAEhD,KAAK,KAAK;4BACR,OAAO,OAAO,CAAC,qBAAqB,CAAC,CAAC,OAAO,CAAA;wBAE/C;4BACE,OAAO,IAAI,CAAA;qBACd;gBACH,CAAC,CAAC,EAAE,CAAA;gBAEJ,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE,CAAC,CAAC,WAAW,KAAK,IAAI,CAAC,CAAC,CAAC,kCAAkB,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,IAAK,WAAmB,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC,CAAA;aACvI;SACF;IACH,CAAC;IAED,WAAW;QACT,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,CAAA;IAC7B,CAAC;IAED,KAAK,CAAC,IAAI,CAAC,IAAY,EAAE,gBAAyB;QAChD,MAAM,WAAW,GAAuB;YACtC,IAAI,EAAE,IAAI;YACV,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,WAAW;YAC9B,IAAI,EAAE,MAAM,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE;YAC5C,OAAO,EAAE,IAAI,CAAC,4BAA4B;SAC3C,CAAA;QAED,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,KAAK,CAAA;QACxC,IAAI,OAAO,IAAI,IAAI,EAAE;YACnB,IAAI,IAAI,CAAC,4BAA4B,CAAC,IAAI,IAAI,IAAI,EAAE;gBAClD,MAAM,sBAAI,CAAC,WAAW,EAAE,IAAI,CAAC,CAAA;aAC9B;iBAAM,IAAI,IAAI,CAAC,gBAAgB,EAAE;gBAChC,MAAM,IAAI,wCAAyB,CACjC,mKAAmK,CACpK,CAAA;aACF;YACD,OAAM;SACP;QAED,IAAI,gBAAgB,IAAI,IAAI,EAAE;YAC5B,gBAAgB,GAAG,SAAS,CAAA;SAC7B;QAED,IAAI,MAAM,IAAI,OAAO,EAAE;YACrB,kBAAG,CAAC,IAAI,CACN;gBACE,IAAI,EAAE,kBAAG,CAAC,QAAQ,CAAC,IAAI,CAAC;gBACxB,eAAe,EAAE,OAAO,CAAC,IAAI;aAC9B,EACD,gBAAgB,CACjB,CAAA;SACF;aAAM;YACL,MAAM,IAAI,GAAG,OAAO,CAAA;YACpB,kBAAG,CAAC,IAAI,CACN;gBACE,IAAI,EAAE,kBAAG,CAAC,QAAQ,CAAC,IAAI,CAAC;gBACxB,OAAO,EAAE,IAAI,CAAC,OAAO;gBACrB,UAAU,EAAE,IAAI,CAAC,UAAU;gBAC3B,KAAK,EAAE,IAAI,CAAC,KAAK;gBACjB,IAAI,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,cAAc;aAClE,EACD,gBAAgB,CACjB,CAAA;SACF;QAED,MAAM,IAAI,CAAC,MAAM,CAAC;YAChB,GAAG,WAAW;YACd,OAAO;YACP,OAAO,EAAE;gBACP,GAAG,IAAI,CAAC,4BAA4B;aACrC;SACF,CAAC,CAAA;IACJ,CAAC;IAEO,KAAK,CAAC,MAAM,CAAC,OAA2B;QAC9C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;YAC1B,IAAI;gBACF,MAAM,sBAAI,CAAC,OAAO,EAAE,IAAI,CAAC,CAAA;gBACzB,MAAK;aACN;YAAC,OAAO,CAAC,EAAE;gBACV,oEAAoE;gBACpE,MAAM,OAAO,GAAG,CAAC,CAAC,OAAO,CAAA;gBACzB,IAAI,OAAO,IAAI,IAAI,IAAI,OAAO,CAAC,QAAQ,CAAC,4BAA4B,CAAC,EAAE;oBACrE,kBAAG,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,aAAa,CAAC,CAAA;oBAC3D,SAAQ;iBACT;gBACD,MAAM,CAAC,CAAA;aACR;SACF;IACH,CAAC;IAED,KAAK,CAAC,oBAAoB,CAAC,IAAY,EAAE,IAAU,EAAE,MAAc,EAAE,YAA4B,EAAE,uBAAwD;QACzJ,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAA;QAE5B,MAAM,KAAK,GAAkB,EAAE,CAAA;QAE/B,MAAM,IAAI,GAAG;YACX,IAAI;YACJ,sBAAsB;YACtB,iBAAiB;YACjB,OAAO,CAAC,WAAW;YACnB,sBAAsB;YACtB,aAAa;YACb,OAAO,CAAC,WAAW;YACnB,sBAAsB;YACtB,gBAAgB;YAChB,OAAO,CAAC,SAAS;YACjB,oBAAoB;YACpB,OAAO,CAAC,YAAY,IAAI,OAAO,CAAC,YAAY;YAC5C,uBAAuB;YACvB,OAAO,CAAC,mBAAmB,IAAI,OAAO,CAAC,4BAA4B,EAAE;SACtE,CAAA;QAED,IAAI,YAAY,IAAI,IAAI,EAAE;YACxB,IAAI,CAAC,IAAI,CAAC,sBAAsB,EAAE,cAAc,EAAE,YAAY,EAAE,sBAAsB,EAAE,kBAAkB,EAAE,EAAE,CAAC,CAAA;SAChH;QAED,IAAI,uBAAuB,IAAI,IAAI,IAAI,uBAAuB,KAAK,WAAW,EAAE;YAC9E,IAAI,CAAC,IAAI,CAAC,iCAAiC,EAAE,uBAAuB,CAAC,CAAA;SACtE;QAED,kBAAG,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,sBAAsB,EAAE,aAAa,EAAE,EAAE,CAAC,CAAC,CAAA;QACpF,kBAAG,CAAC,IAAI,CAAC,4BAA4B,CAAC,eAAe,EAAE,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,sBAAsB,EAAE,iBAAiB,EAAE,EAAE,CAAC,CAAC,CAAA;QACtH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,WAAW,EAAE,CAAA;QACzC,kBAAG,CAAC,QAAQ,EAAE,EAAE,CAAC,EAAE;YACjB,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;YACd,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,EAAE,CAAC,CAAA;QAC7B,CAAC,CAAC,CAAA;QAEF,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAA;QAC1B,MAAM,qBAAqB,GAAG,CAAC,2BAAmB,EAAE,IAAI,IAAI,IAAI,MAAM,CAAC,YAAY,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,IAAI,CAAC,OAAO,CAAC,KAAK,CAAA;QAC7H,IAAI,iBAAiB,GAA6B,IAAI,CAAA;QACtD,8GAA8G;QAC9G,IAAI,qBAAqB,IAAI,IAAI,EAAE;YACjC,MAAM,OAAO,GAAI,qBAA6C,CAAC,IAAI,CAAA;YACnE,IAAI,OAAO,IAAI,IAAI,EAAE;gBACnB,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;aACpB;YAED,MAAM,KAAK,GAAG,YAAI,CAAC,kBAAkB,CAAC,CAAA;YACtC,MAAM,IAAI,GAAG,mBAAU,CAAC,QAAQ,CAAC,CAAA;YACjC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,eAAe,IAAI,oBAAoB,CAAC,CAAA;YAC3D,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC,CAAA;YAC9D,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAA;YACjC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,4BAA4B,CAAC,eAAe,IAAI,oBAAoB,CAAC,CAAA;YACtF,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,4BAA4B,CAAC,sBAAsB,IAAI,gBAAgB,CAAC,CAAA;YAEzF,iBAAiB,GAAG,IAAI,gCAAiB,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI,CAAC,CAAA;YAC7D,IAAI,MAAM,iBAAiB,CAAC,WAAW,CAAC,MAAM,qBAAM,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,EAAE;gBAClE,KAAK,CAAC,GAAG,EAAE,CAAA;gBACX,OAAM;aACP;YACD,KAAK,CAAC,GAAG,EAAE,CAAA;SACZ;QAED,MAAM,KAAK,GAAG,YAAI,CAAC,WAAW,CAAC,CAAA;QAC/B,8DAA8D;QAC9D,IAAI,OAAO,CAAC,QAAQ,KAAK,OAAO,IAAI,OAAO,CAAC,QAAQ,KAAK,QAAQ,EAAE;YACjE,MAAM,gCAAiB,CAAC,CAAC,QAAQ,EAAE,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,EAAE,SAAS,CAAC,mBAAmB,EAAE,EAAE,EAAE,CAAC,CAAC,uBAAuB,CAAC,CAAA;SAClI;aAAM,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,KAAK,UAAU,EAAE;YAClD,MAAM,UAAU,GAAG,MAAM,mCAAiB,EAAE,CAAA;YAC5C,MAAM,eAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,iBAAiB,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,gBAAgB,CAAC,EAAE,IAAI,CAAC,CAAA;SACxG;QAED,MAAM,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QACrB,KAAK,CAAC,GAAG,EAAE,CAAA;QAEX,IAAI,iBAAiB,IAAI,IAAI,EAAE;YAC7B,MAAM,iBAAiB,CAAC,IAAI,EAAE,CAAA;SAC/B;IACH,CAAC;IAEO,UAAU;QAChB,OAAO,IAAI,CAAC,4BAA4B,CAAC,QAAQ,KAAK,IAAI,CAAA;IAC5D,CAAC;IAES,8BAA8B,CAAC,WAA6B;QACpE,IAAI,IAAI,CAAC,4BAA4B,CAAC,qBAAqB,KAAK,KAAK,EAAE;YACrE,OAAO,IAAI,CAAA;SACZ;QAED,OAAO,IAAI,CAAC,EAAE;YACZ,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE;gBACzE,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;gBACpC,IAAI,SAAS,KAAK,WAAW,CAAC,SAAS,EAAE;oBACvC,OAAO,IAAI,wBAAmB,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAA;iBACxD;aACF;YACD,OAAO,IAAI,CAAA;QACb,CAAC,CAAA;IACH,CAAC;IAES,KAAK,CAAC,OAAO,CAAC,WAA6B,EAAE,MAAe;QACpE,MAAM,WAAW,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,MAAM,CAAA;QACzD,IAAI,IAAI,CAAC,4BAA4B,CAAC,qBAAqB,KAAK,KAAK,EAAE;YACrE,OAAM;SACP;QAED,MAAM,sBAAe,CAAC,GAAG,CAAC,kBAAO,CAAC,WAAW,CAAC,SAAS,CAAC,EAAE,CAAC,IAAY,EAAO,EAAE;YAC9E,IAAI,IAAI,KAAK,WAAW,EAAE;gBACxB,OAAO,IAAI,CAAC,oBAAoB,CAC9B,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE,WAAW,CAAC,EAC7C,WAAW,CAAC,IAAI,EAChB,WAAW,CAAC,MAAM,EAClB,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,MAAM,CAAC,EAClC,IAAI,CAAC,4BAA4B,CAAC,uBAAuB,CAC1D,CAAA;aACF;iBAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE;gBAChF,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC,CAAA;aACzD;YACD,OAAO,IAAI,CAAA;QACb,CAAC,CAAC,CAAA;QAEF,IAAI,CAAC,MAAM,EAAE;YACX,OAAM;SACP;QAED,MAAM,WAAW,GAAG,CAAC,QAAkB,EAAE,EAAE;YACzC,MAAM,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE,GAAG,QAAQ,CAAC,CAAA;YAC5D,OAAO,SAAI,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAA;QAClI,CAAC,CAAA;QACD,MAAM,WAAW,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC,WAAW,CAAC,CAAC,WAAW,EAAE,mBAAmB,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAA;QACtH,MAAM,sBAAe,CAAC,GAAG,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,WAAW,EAAE,CAAC,EAAE,CAAC,CAAA;IAC7F,CAAC;CACF;AA7WD,kCA6WC", "sourcesContent": ["import BluebirdPromise from \"bluebird-lst\"\nimport { Arch, asArray, InvalidConfigurationError, log, use, executeAppBuilder } from \"builder-util\"\nimport { parseDn } from \"builder-util-runtime\"\nimport { CopyFileTransformer, FileTransformer, walk } from \"builder-util/out/fs\"\nimport { createHash } from \"crypto\"\nimport { readdir } from \"fs/promises\"\nimport * as isCI from \"is-ci\"\nimport { Lazy } from \"lazy-val\"\nimport * as path from \"path\"\nimport { importCertificate } from \"./codeSign/codesign\"\nimport {\n  CertificateFromStoreInfo,\n  CertificateInfo,\n  FileCodeSigningInfo,\n  getCertificateFromStoreInfo,\n  getCertInfo,\n  getSignVendorPath,\n  sign,\n  WindowsSignOptions,\n} from \"./codeSign/windowsCodeSign\"\nimport { AfterPackContext } from \"./configuration\"\nimport { DIR_TARGET, Platform, Target } from \"./core\"\nimport { RequestedExecutionLevel, WindowsConfiguration } from \"./options/winOptions\"\nimport { Packager } from \"./packager\"\nimport { chooseNotNull, PlatformPackager } from \"./platformPackager\"\nimport AppXTarget from \"./targets/AppxTarget\"\nimport { NsisTarget } from \"./targets/nsis/NsisTarget\"\nimport { AppPackageHelper, CopyElevateHelper } from \"./targets/nsis/nsisUtil\"\nimport { WebInstallerTarget } from \"./targets/nsis/WebInstallerTarget\"\nimport { createCommonTarget } from \"./targets/targetFactory\"\nimport { BuildCacheManager, digest } from \"./util/cacheManager\"\nimport { isBuildCacheEnabled } from \"./util/flags\"\nimport { time } from \"./util/timer\"\nimport { getWindowsVm, VmManager } from \"./vm/vm\"\nimport { execWine } from \"./wine\"\n\nexport class WinPackager extends PlatformPackager<WindowsConfiguration> {\n  readonly cscInfo = new Lazy<FileCodeSigningInfo | CertificateFromStoreInfo | null>(() => {\n    const platformSpecificBuildOptions = this.platformSpecificBuildOptions\n    if (platformSpecificBuildOptions.certificateSubjectName != null || platformSpecificBuildOptions.certificateSha1 != null) {\n      return this.vm.value\n        .then(vm => getCertificateFromStoreInfo(platformSpecificBuildOptions, vm))\n        .catch(e => {\n          // https://github.com/electron-userland/electron-builder/pull/2397\n          if (platformSpecificBuildOptions.sign == null) {\n            throw e\n          } else {\n            log.debug({ error: e }, \"getCertificateFromStoreInfo error\")\n            return null\n          }\n        })\n    }\n\n    const certificateFile = platformSpecificBuildOptions.certificateFile\n    if (certificateFile != null) {\n      const certificatePassword = this.getCscPassword()\n      return Promise.resolve({\n        file: certificateFile,\n        password: certificatePassword == null ? null : certificatePassword.trim(),\n      })\n    }\n\n    const cscLink = this.getCscLink(\"WIN_CSC_LINK\")\n    if (cscLink == null) {\n      return Promise.resolve(null)\n    }\n\n    return (\n      importCertificate(cscLink, this.info.tempDirManager, this.projectDir)\n        // before then\n        .catch(e => {\n          if (e instanceof InvalidConfigurationError) {\n            throw new InvalidConfigurationError(`Env WIN_CSC_LINK is not correct, cannot resolve: ${e.message}`)\n          } else {\n            throw e\n          }\n        })\n        .then(path => {\n          return {\n            file: path,\n            password: this.getCscPassword(),\n          }\n        })\n    )\n  })\n\n  private _iconPath = new Lazy(() => this.getOrConvertIcon(\"ico\"))\n\n  readonly vm = new Lazy<VmManager>(() => (process.platform === \"win32\" ? Promise.resolve(new VmManager()) : getWindowsVm(this.debugLogger)))\n\n  readonly computedPublisherName = new Lazy<Array<string> | null>(async () => {\n    const publisherName = this.platformSpecificBuildOptions.publisherName\n    if (publisherName === null) {\n      return null\n    } else if (publisherName != null) {\n      return asArray(publisherName)\n    }\n\n    const certInfo = await this.lazyCertInfo.value\n    return certInfo == null ? null : [certInfo.commonName]\n  })\n\n  readonly lazyCertInfo = new Lazy<CertificateInfo | null>(async () => {\n    const cscInfo = await this.cscInfo.value\n    if (cscInfo == null) {\n      return null\n    }\n\n    if (\"subject\" in cscInfo) {\n      const bloodyMicrosoftSubjectDn = cscInfo.subject\n      return {\n        commonName: parseDn(bloodyMicrosoftSubjectDn).get(\"CN\")!,\n        bloodyMicrosoftSubjectDn,\n      }\n    }\n\n    const cscFile = cscInfo.file\n    if (cscFile == null) {\n      return null\n    }\n    return await getCertInfo(cscFile, cscInfo.password || \"\")\n  })\n\n  get isForceCodeSigningVerification(): boolean {\n    return this.platformSpecificBuildOptions.verifyUpdateCodeSignature !== false\n  }\n\n  constructor(info: Packager) {\n    super(info, Platform.WINDOWS)\n  }\n\n  get defaultTarget(): Array<string> {\n    return [\"nsis\"]\n  }\n\n  protected doGetCscPassword(): string | undefined | null {\n    return chooseNotNull(chooseNotNull(this.platformSpecificBuildOptions.certificatePassword, process.env.WIN_CSC_KEY_PASSWORD), super.doGetCscPassword())\n  }\n\n  createTargets(targets: Array<string>, mapper: (name: string, factory: (outDir: string) => Target) => void): void {\n    let copyElevateHelper: CopyElevateHelper | null\n    const getCopyElevateHelper = () => {\n      if (copyElevateHelper == null) {\n        copyElevateHelper = new CopyElevateHelper()\n      }\n      return copyElevateHelper\n    }\n\n    let helper: AppPackageHelper | null\n    const getHelper = () => {\n      if (helper == null) {\n        helper = new AppPackageHelper(getCopyElevateHelper())\n      }\n      return helper\n    }\n\n    for (const name of targets) {\n      if (name === DIR_TARGET) {\n        continue\n      }\n\n      if (name === \"nsis\" || name === \"portable\") {\n        mapper(name, outDir => new NsisTarget(this, outDir, name, getHelper()))\n      } else if (name === \"nsis-web\") {\n        // package file format differs from nsis target\n        mapper(name, outDir => new WebInstallerTarget(this, path.join(outDir, name), name, new AppPackageHelper(getCopyElevateHelper())))\n      } else {\n        const targetClass: typeof NsisTarget | typeof AppXTarget | null = (() => {\n          switch (name) {\n            case \"squirrel\":\n              try {\n                return require(\"electron-builder-squirrel-windows\").default\n              } catch (e) {\n                throw new InvalidConfigurationError(`Module electron-builder-squirrel-windows must be installed in addition to build Squirrel.Windows: ${e.stack || e}`)\n              }\n\n            case \"appx\":\n              return require(\"./targets/AppxTarget\").default\n\n            case \"msi\":\n              return require(\"./targets/MsiTarget\").default\n\n            default:\n              return null\n          }\n        })()\n\n        mapper(name, outDir => (targetClass === null ? createCommonTarget(name, outDir, this) : new (targetClass as any)(this, outDir, name)))\n      }\n    }\n  }\n\n  getIconPath() {\n    return this._iconPath.value\n  }\n\n  async sign(file: string, logMessagePrefix?: string): Promise<void> {\n    const signOptions: WindowsSignOptions = {\n      path: file,\n      name: this.appInfo.productName,\n      site: await this.appInfo.computePackageUrl(),\n      options: this.platformSpecificBuildOptions,\n    }\n\n    const cscInfo = await this.cscInfo.value\n    if (cscInfo == null) {\n      if (this.platformSpecificBuildOptions.sign != null) {\n        await sign(signOptions, this)\n      } else if (this.forceCodeSigning) {\n        throw new InvalidConfigurationError(\n          `App is not signed and \"forceCodeSigning\" is set to true, please ensure that code signing configuration is correct, please see https://electron.build/code-signing`\n        )\n      }\n      return\n    }\n\n    if (logMessagePrefix == null) {\n      logMessagePrefix = \"signing\"\n    }\n\n    if (\"file\" in cscInfo) {\n      log.info(\n        {\n          file: log.filePath(file),\n          certificateFile: cscInfo.file,\n        },\n        logMessagePrefix\n      )\n    } else {\n      const info = cscInfo\n      log.info(\n        {\n          file: log.filePath(file),\n          subject: info.subject,\n          thumbprint: info.thumbprint,\n          store: info.store,\n          user: info.isLocalMachineStore ? \"local machine\" : \"current user\",\n        },\n        logMessagePrefix\n      )\n    }\n\n    await this.doSign({\n      ...signOptions,\n      cscInfo,\n      options: {\n        ...this.platformSpecificBuildOptions,\n      },\n    })\n  }\n\n  private async doSign(options: WindowsSignOptions) {\n    for (let i = 0; i < 3; i++) {\n      try {\n        await sign(options, this)\n        break\n      } catch (e) {\n        // https://github.com/electron-userland/electron-builder/issues/1414\n        const message = e.message\n        if (message != null && message.includes(\"Couldn't resolve host name\")) {\n          log.warn({ error: message, attempt: i + 1 }, `cannot sign`)\n          continue\n        }\n        throw e\n      }\n    }\n  }\n\n  async signAndEditResources(file: string, arch: Arch, outDir: string, internalName?: string | null, requestedExecutionLevel?: RequestedExecutionLevel | null) {\n    const appInfo = this.appInfo\n\n    const files: Array<string> = []\n\n    const args = [\n      file,\n      \"--set-version-string\",\n      \"FileDescription\",\n      appInfo.productName,\n      \"--set-version-string\",\n      \"ProductName\",\n      appInfo.productName,\n      \"--set-version-string\",\n      \"LegalCopyright\",\n      appInfo.copyright,\n      \"--set-file-version\",\n      appInfo.shortVersion || appInfo.buildVersion,\n      \"--set-product-version\",\n      appInfo.shortVersionWindows || appInfo.getVersionInWeirdWindowsForm(),\n    ]\n\n    if (internalName != null) {\n      args.push(\"--set-version-string\", \"InternalName\", internalName, \"--set-version-string\", \"OriginalFilename\", \"\")\n    }\n\n    if (requestedExecutionLevel != null && requestedExecutionLevel !== \"asInvoker\") {\n      args.push(\"--set-requested-execution-level\", requestedExecutionLevel)\n    }\n\n    use(appInfo.companyName, it => args.push(\"--set-version-string\", \"CompanyName\", it))\n    use(this.platformSpecificBuildOptions.legalTrademarks, it => args.push(\"--set-version-string\", \"LegalTrademarks\", it))\n    const iconPath = await this.getIconPath()\n    use(iconPath, it => {\n      files.push(it)\n      args.push(\"--set-icon\", it)\n    })\n\n    const config = this.config\n    const cscInfoForCacheDigest = !isBuildCacheEnabled() || isCI || config.electronDist != null ? null : await this.cscInfo.value\n    let buildCacheManager: BuildCacheManager | null = null\n    // resources editing doesn't change executable for the same input and executed quickly - no need to complicate\n    if (cscInfoForCacheDigest != null) {\n      const cscFile = (cscInfoForCacheDigest as FileCodeSigningInfo).file\n      if (cscFile != null) {\n        files.push(cscFile)\n      }\n\n      const timer = time(\"executable cache\")\n      const hash = createHash(\"sha512\")\n      hash.update(config.electronVersion || \"no electronVersion\")\n      hash.update(JSON.stringify(this.platformSpecificBuildOptions))\n      hash.update(JSON.stringify(args))\n      hash.update(this.platformSpecificBuildOptions.certificateSha1 || \"no certificateSha1\")\n      hash.update(this.platformSpecificBuildOptions.certificateSubjectName || \"no subjectName\")\n\n      buildCacheManager = new BuildCacheManager(outDir, file, arch)\n      if (await buildCacheManager.copyIfValid(await digest(hash, files))) {\n        timer.end()\n        return\n      }\n      timer.end()\n    }\n\n    const timer = time(\"wine&sign\")\n    // rcedit crashed of executed using wine, resourcehacker works\n    if (process.platform === \"win32\" || process.platform === \"darwin\") {\n      await executeAppBuilder([\"rcedit\", \"--args\", JSON.stringify(args)], undefined /* child-process */, {}, 3 /* retry three times */)\n    } else if (this.info.framework.name === \"electron\") {\n      const vendorPath = await getSignVendorPath()\n      await execWine(path.join(vendorPath, \"rcedit-ia32.exe\"), path.join(vendorPath, \"rcedit-x64.exe\"), args)\n    }\n\n    await this.sign(file)\n    timer.end()\n\n    if (buildCacheManager != null) {\n      await buildCacheManager.save()\n    }\n  }\n\n  private isSignDlls(): boolean {\n    return this.platformSpecificBuildOptions.signDlls === true\n  }\n\n  protected createTransformerForExtraFiles(packContext: AfterPackContext): FileTransformer | null {\n    if (this.platformSpecificBuildOptions.signAndEditExecutable === false) {\n      return null\n    }\n\n    return file => {\n      if (file.endsWith(\".exe\") || (this.isSignDlls() && file.endsWith(\".dll\"))) {\n        const parentDir = path.dirname(file)\n        if (parentDir !== packContext.appOutDir) {\n          return new CopyFileTransformer(file => this.sign(file))\n        }\n      }\n      return null\n    }\n  }\n\n  protected async signApp(packContext: AfterPackContext, isAsar: boolean): Promise<any> {\n    const exeFileName = `${this.appInfo.productFilename}.exe`\n    if (this.platformSpecificBuildOptions.signAndEditExecutable === false) {\n      return\n    }\n\n    await BluebirdPromise.map(readdir(packContext.appOutDir), (file: string): any => {\n      if (file === exeFileName) {\n        return this.signAndEditResources(\n          path.join(packContext.appOutDir, exeFileName),\n          packContext.arch,\n          packContext.outDir,\n          path.basename(exeFileName, \".exe\"),\n          this.platformSpecificBuildOptions.requestedExecutionLevel\n        )\n      } else if (file.endsWith(\".exe\") || (this.isSignDlls() && file.endsWith(\".dll\"))) {\n        return this.sign(path.join(packContext.appOutDir, file))\n      }\n      return null\n    })\n\n    if (!isAsar) {\n      return\n    }\n\n    const signPromise = (filepath: string[]) => {\n      const outDir = path.join(packContext.appOutDir, ...filepath)\n      return walk(outDir, (file, stat) => stat.isDirectory() || file.endsWith(\".exe\") || (this.isSignDlls() && file.endsWith(\".dll\")))\n    }\n    const filesToSign = await Promise.all([signPromise([\"resources\", \"app.asar.unpacked\"]), signPromise([\"swiftshader\"])])\n    await BluebirdPromise.map(filesToSign.flat(1), file => this.sign(file), { concurrency: 4 })\n  }\n}\n"]}