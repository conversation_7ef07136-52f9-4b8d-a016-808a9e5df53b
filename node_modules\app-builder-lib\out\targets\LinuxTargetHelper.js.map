{"version": 3, "file": "LinuxTargetHelper.js", "sourceRoot": "", "sources": ["../../src/targets/LinuxTargetHelper.ts"], "names": [], "mappings": ";;;AAAA,+CAAoE;AACpE,uCAAqC;AACrC,uCAA+B;AAI/B,+BAA2B;AAEd,QAAA,aAAa,GAAG,MAAM,CAAA;AAEnC,MAAa,iBAAiB;IAO5B,YAAoB,QAAuB;QAAvB,aAAQ,GAAR,QAAQ,CAAe;QAN1B,gBAAW,GAAG,IAAI,eAAI,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,mBAAmB,EAAE,CAAC,CAAA;QAExD,yBAAoB,GAAG,IAAI,eAAI,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,oBAAoB,EAAE,CAAC,CAAA;QAEnF,gBAAW,GAAkB,IAAI,CAAA;IAEa,CAAC;IAE/C,IAAI,KAAK;QACP,OAAO,IAAI,CAAC,WAAW,CAAC,KAAK,CAAA;IAC/B,CAAC;IAED,IAAI,aAAa;QACf,OAAO,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAA;IACxC,CAAC;IAEO,KAAK,CAAC,oBAAoB;QAChC,MAAM,KAAK,GAAkB,EAAE,CAAA;QAC/B,KAAK,MAAM,eAAe,IAAI,IAAI,CAAC,QAAQ,CAAC,gBAAgB,EAAE;YAC5D,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE;gBAC7B,SAAQ;aACT;YAED,MAAM,IAAI,GAAG,oBAAoB,eAAe,CAAC,QAAQ;qBAC1C,eAAe,CAAC,GAAG;MAClC,eAAe,CAAC,WAAW,CAAC,CAAC,CAAC,YAAY,eAAe,CAAC,WAAW,YAAY,CAAC,CAAC,CAAC,EAAE;;aAE/E,CAAA;YACP,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;SACjB;QAED,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;YACtB,OAAO,IAAI,CAAA;SACZ;QAED,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,MAAM,CAAC,CAAA;QACpD,MAAM,qBAAU,CACd,IAAI,EACJ,qHAAqH,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,gBAAgB,CAC5J,CAAA;QACD,OAAO,IAAI,CAAA;IACb,CAAC;IAED,sFAAsF;IAC9E,KAAK,CAAC,mBAAmB;;QAC/B,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAA;QAC9B,MAAM,EAAE,4BAA4B,EAAE,MAAM,EAAE,GAAG,QAAQ,CAAA;QAEzD,MAAM,OAAO,GAAG,CAAC,4BAA4B,CAAC,IAAI,EAAE,MAAA,MAAA,MAAM,CAAC,GAAG,0CAAE,IAAI,mCAAI,MAAM,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAa,CAAA;QAErH,wGAAwG;QACxG,IAAI,eAAe,GAAG,CAAC,GAAG,sBAAO,CAAC,QAAQ,CAAC,uBAAuB,EAAE,CAAC,CAAC,CAAA;QACtE,MAAM,cAAc,GAAG,MAAA,MAAM,CAAC,WAAW,0CAAE,cAAc,CAAA;QACzD,IAAI,cAAc,IAAI,CAAC,MAAM,qBAAM,CAAC,WAAI,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE;YACnE,eAAe,GAAG,CAAC,cAAc,EAAE,GAAG,eAAe,CAAC,CAAA;SACvD;QAED,yEAAyE;QACzE,MAAM,MAAM,GAAG,MAAM,QAAQ,CAAC,WAAW,CAAC,OAAO,EAAE,eAAe,EAAE,KAAK,CAAC,CAAA;QAC1E,IAAI,CAAC,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,IAAI,CAAA;QACjD,OAAO,MAAM,CAAA;IACf,CAAC;IAED,cAAc,CAAC,OAAmC;QAChD,OAAO,OAAO,CAAC,WAAW,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,WAAW,CAAA;IACjE,CAAC;IAED,KAAK,CAAC,iBAAiB,CAAC,qBAAiD,EAAE,IAAa,EAAE,WAA2B,EAAE,KAAiC;QACtJ,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,qBAAqB,EAAE,IAAI,EAAE,KAAK,CAAC,CAAA;QAC/E,MAAM,IAAI,GAAG,WAAW,IAAI,CAAC,MAAM,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,eAAe,UAAU,CAAC,CAAC,CAAA;QACjH,MAAM,qBAAU,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;QAC5B,OAAO,IAAI,CAAA;IACb,CAAC;IAED,mBAAmB,CAAC,qBAAiD,EAAE,IAAa,EAAE,KAAiC;QACrH,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;YACrC,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAA;SAC3C;QACD,oEAAoE;QACpE,IAAI,qBAAqB,CAAC,OAAO,IAAI,IAAI,IAAI,qBAAqB,CAAC,OAAO,CAAC,IAAI,IAAI,IAAI,EAAE;YACvF,MAAM,IAAI,KAAK,CAAC,sFAAsF,CAAC,CAAA;SACxG;QAED,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAA;QAC9B,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAA;QAEhC,MAAM,cAAc,GAAG,qBAAqB,CAAC,cAAc,CAAA;QAC3D,IAAI,IAAI,IAAI,IAAI,EAAE;YAChB,IAAI,GAAG,GAAG,qBAAa,IAAI,OAAO,CAAC,oBAAoB,IAAI,QAAQ,CAAC,cAAc,EAAE,CAAA;YACpF,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;gBACpC,IAAI,GAAG,IAAI,IAAI,GAAG,CAAA;aACnB;YACD,IAAI,cAAc,EAAE;gBAClB,IAAI,IAAI,GAAG,CAAA;gBACX,IAAI,IAAI,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;aACjC;YACD,0GAA0G;YAC1G,MAAM,SAAS,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAA;YAC1C,IAAI,cAAc,IAAI,IAAI,IAAI,cAAc,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,CAAC,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE;gBAC7F,IAAI,IAAI,KAAK,CAAA;aACd;SACF;QAED,MAAM,WAAW,GAAQ;YACvB,IAAI,EAAE,OAAO,CAAC,WAAW;YACzB,IAAI,EAAE,IAAI;YACV,QAAQ,EAAE,OAAO;YACjB,IAAI,EAAE,aAAa;YACnB,IAAI,EAAE,QAAQ,CAAC,cAAc;YAC7B,mGAAmG;YACnG,yEAAyE;YACzE,oDAAoD;YACpD,0CAA0C;YAC1C,2FAA2F;YAC3F,cAAc,EAAE,OAAO,CAAC,WAAW;YACnC,GAAG,KAAK;YACR,GAAG,qBAAqB,CAAC,OAAO;SACjC,CAAA;QAED,MAAM,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC,qBAAqB,CAAC,CAAA;QAC9D,IAAI,CAAC,8BAAe,CAAC,WAAW,CAAC,EAAE;YACjC,WAAW,CAAC,OAAO,GAAG,WAAW,CAAA;SAClC;QAED,MAAM,SAAS,GAAkB,sBAAO,CAAC,qBAAqB,CAAC,SAAS,CAAC,CAAA;QACzE,KAAK,MAAM,eAAe,IAAI,QAAQ,CAAC,gBAAgB,EAAE;YACvD,IAAI,eAAe,CAAC,QAAQ,IAAI,IAAI,EAAE;gBACpC,SAAS,CAAC,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAA;aACzC;SACF;QAED,KAAK,MAAM,QAAQ,IAAI,sBAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,sBAAO,CAAC,QAAQ,CAAC,4BAA4B,CAAC,SAAS,CAAC,CAAC,EAAE;YAC1H,KAAK,MAAM,MAAM,IAAI,sBAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE;gBAC9C,SAAS,CAAC,IAAI,CAAC,oBAAoB,MAAM,EAAE,CAAC,CAAA;aAC7C;SACF;QAED,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;YAC1B,WAAW,CAAC,QAAQ,GAAG,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,CAAA;SACjD;QAED,IAAI,QAAQ,GAAG,qBAAqB,CAAC,QAAQ,CAAA;QAC7C,IAAI,8BAAe,CAAC,QAAQ,CAAC,EAAE;YAC7B,MAAM,WAAW,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,IAAI,EAAE,CAAC,CAAC,QAAQ,CAAA;YACxD,IAAI,WAAW,IAAI,IAAI,EAAE;gBACvB,QAAQ,GAAG,kBAAkB,CAAC,WAAW,CAAC,CAAA;aAC3C;YAED,IAAI,QAAQ,IAAI,IAAI,EAAE;gBACpB,6DAA6D;gBAC7D,IAAI,WAAW,IAAI,IAAI,EAAE;oBACvB,kBAAG,CAAC,IAAI,CAAC,EAAE,WAAW,EAAE,EAAE,wGAAwG,CAAC,CAAA;iBACpI;gBACD,kBAAG,CAAC,IAAI,CACN;oBACE,MAAM,EAAE,qDAAqD;oBAC7D,IAAI,EAAE,gDAAgD;iBACvD,EACD,wDAAwD,CACzD,CAAA;gBACD,QAAQ,GAAG,SAAS,CAAA;aACrB;SACF;QACD,WAAW,CAAC,UAAU,GAAG,GAAG,QAAQ,GAAG,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,CAAA;QAE1E,IAAI,IAAI,GAAG,iBAAiB,CAAA;QAC5B,KAAK,MAAM,IAAI,IAAI,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE;YAC3C,IAAI,IAAI,KAAK,IAAI,IAAI,WAAW,CAAC,IAAI,CAAC,EAAE,CAAA;SACzC;QACD,IAAI,IAAI,IAAI,CAAA;QACZ,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;IAC9B,CAAC;CACF;AA7KD,8CA6KC;AAED,MAAM,kBAAkB,GAAQ;IAC9B,qCAAqC,EAAE,UAAU;IACjD,qCAAqC,EAAE,aAAa;IACpD,+BAA+B,EAAE,WAAW;IAC5C,2BAA2B,EAAE,MAAM;IACnC,2BAA2B,EAAE,kBAAkB;IAC/C,+BAA+B,EAAE,SAAS;IAC1C,uCAAuC,EAAE,cAAc;IACvD,6BAA6B,EAAE,gBAAgB;CAChD,CAAA", "sourcesContent": ["import { asArray, isEmptyOrSpaces, log, exists } from \"builder-util\"\nimport { outputFile } from \"fs-extra\"\nimport { Lazy } from \"lazy-val\"\nimport { LinuxPackager } from \"../linuxPackager\"\nimport { LinuxTargetSpecificOptions } from \"../options/linuxOptions\"\nimport { IconInfo } from \"../platformPackager\"\nimport { join } from \"path\"\n\nexport const installPrefix = \"/opt\"\n\nexport class LinuxTargetHelper {\n  private readonly iconPromise = new Lazy(() => this.computeDesktopIcons())\n\n  private readonly mimeTypeFilesPromise = new Lazy(() => this.computeMimeTypeFiles())\n\n  maxIconPath: string | null = null\n\n  constructor(private packager: LinuxPackager) {}\n\n  get icons(): Promise<Array<IconInfo>> {\n    return this.iconPromise.value\n  }\n\n  get mimeTypeFiles(): Promise<string | null> {\n    return this.mimeTypeFilesPromise.value\n  }\n\n  private async computeMimeTypeFiles(): Promise<string | null> {\n    const items: Array<string> = []\n    for (const fileAssociation of this.packager.fileAssociations) {\n      if (!fileAssociation.mimeType) {\n        continue\n      }\n\n      const data = `<mime-type type=\"${fileAssociation.mimeType}\">\n  <glob pattern=\"*.${fileAssociation.ext}\"/>\n    ${fileAssociation.description ? `<comment>${fileAssociation.description}</comment>` : \"\"}\n  <icon name=\"x-office-document\" />\n</mime-type>`\n      items.push(data)\n    }\n\n    if (items.length === 0) {\n      return null\n    }\n\n    const file = await this.packager.getTempFile(\".xml\")\n    await outputFile(\n      file,\n      '<?xml version=\"1.0\" encoding=\"utf-8\"?>\\n<mime-info xmlns=\"http://www.freedesktop.org/standards/shared-mime-info\">\\n' + items.join(\"\\n\") + \"\\n</mime-info>\"\n    )\n    return file\n  }\n\n  // must be name without spaces and other special characters, but not product name used\n  private async computeDesktopIcons(): Promise<Array<IconInfo>> {\n    const packager = this.packager\n    const { platformSpecificBuildOptions, config } = packager\n\n    const sources = [platformSpecificBuildOptions.icon, config.mac?.icon ?? config.icon].filter(str => !!str) as string[]\n\n    // If no explicit sources are defined, fallback to buildResources directory, then default framework icon\n    let fallbackSources = [...asArray(packager.getDefaultFrameworkIcon())]\n    const buildResources = config.directories?.buildResources\n    if (buildResources && (await exists(join(buildResources, \"icons\")))) {\n      fallbackSources = [buildResources, ...fallbackSources]\n    }\n\n    // need to put here and not as default because need to resolve image size\n    const result = await packager.resolveIcon(sources, fallbackSources, \"set\")\n    this.maxIconPath = result[result.length - 1].file\n    return result\n  }\n\n  getDescription(options: LinuxTargetSpecificOptions) {\n    return options.description || this.packager.appInfo.description\n  }\n\n  async writeDesktopEntry(targetSpecificOptions: LinuxTargetSpecificOptions, exec?: string, destination?: string | null, extra?: { [key: string]: string }): Promise<string> {\n    const data = await this.computeDesktopEntry(targetSpecificOptions, exec, extra)\n    const file = destination || (await this.packager.getTempFile(`${this.packager.appInfo.productFilename}.desktop`))\n    await outputFile(file, data)\n    return file\n  }\n\n  computeDesktopEntry(targetSpecificOptions: LinuxTargetSpecificOptions, exec?: string, extra?: { [key: string]: string }): Promise<string> {\n    if (exec != null && exec.length === 0) {\n      throw new Error(\"Specified exec is empty\")\n    }\n    // https://github.com/electron-userland/electron-builder/issues/3418\n    if (targetSpecificOptions.desktop != null && targetSpecificOptions.desktop.Exec != null) {\n      throw new Error(\"Please specify executable name as linux.executableName instead of linux.desktop.Exec\")\n    }\n\n    const packager = this.packager\n    const appInfo = packager.appInfo\n\n    const executableArgs = targetSpecificOptions.executableArgs\n    if (exec == null) {\n      exec = `${installPrefix}/${appInfo.sanitizedProductName}/${packager.executableName}`\n      if (!/^[/0-9A-Za-z._-]+$/.test(exec)) {\n        exec = `\"${exec}\"`\n      }\n      if (executableArgs) {\n        exec += \" \"\n        exec += executableArgs.join(\" \")\n      }\n      // https://specifications.freedesktop.org/desktop-entry-spec/desktop-entry-spec-latest.html#exec-variables\n      const execCodes = [\"%f\", \"%u\", \"%F\", \"%U\"]\n      if (executableArgs == null || executableArgs.findIndex(arg => execCodes.includes(arg)) === -1) {\n        exec += \" %U\"\n      }\n    }\n\n    const desktopMeta: any = {\n      Name: appInfo.productName,\n      Exec: exec,\n      Terminal: \"false\",\n      Type: \"Application\",\n      Icon: packager.executableName,\n      // https://askubuntu.com/questions/367396/what-represent-the-startupwmclass-field-of-a-desktop-file\n      // must be set to package.json name (because it is Electron set WM_CLASS)\n      // to get WM_CLASS of running window: xprop WM_CLASS\n      // StartupWMClass doesn't work for unicode\n      // https://github.com/electron/electron/blob/2-0-x/atom/browser/native_window_views.cc#L226\n      StartupWMClass: appInfo.productName,\n      ...extra,\n      ...targetSpecificOptions.desktop,\n    }\n\n    const description = this.getDescription(targetSpecificOptions)\n    if (!isEmptyOrSpaces(description)) {\n      desktopMeta.Comment = description\n    }\n\n    const mimeTypes: Array<string> = asArray(targetSpecificOptions.mimeTypes)\n    for (const fileAssociation of packager.fileAssociations) {\n      if (fileAssociation.mimeType != null) {\n        mimeTypes.push(fileAssociation.mimeType)\n      }\n    }\n\n    for (const protocol of asArray(packager.config.protocols).concat(asArray(packager.platformSpecificBuildOptions.protocols))) {\n      for (const scheme of asArray(protocol.schemes)) {\n        mimeTypes.push(`x-scheme-handler/${scheme}`)\n      }\n    }\n\n    if (mimeTypes.length !== 0) {\n      desktopMeta.MimeType = mimeTypes.join(\";\") + \";\"\n    }\n\n    let category = targetSpecificOptions.category\n    if (isEmptyOrSpaces(category)) {\n      const macCategory = (packager.config.mac || {}).category\n      if (macCategory != null) {\n        category = macToLinuxCategory[macCategory]\n      }\n\n      if (category == null) {\n        // https://github.com/develar/onshape-desktop-shell/issues/48\n        if (macCategory != null) {\n          log.warn({ macCategory }, \"cannot map macOS category to Linux. If possible mapping is known for you, please file issue to add it.\")\n        }\n        log.warn(\n          {\n            reason: \"linux.category is not set and cannot map from macOS\",\n            docs: \"https://www.electron.build/configuration/linux\",\n          },\n          'application Linux category is set to default \"Utility\"'\n        )\n        category = \"Utility\"\n      }\n    }\n    desktopMeta.Categories = `${category}${category.endsWith(\";\") ? \"\" : \";\"}`\n\n    let data = `[Desktop Entry]`\n    for (const name of Object.keys(desktopMeta)) {\n      data += `\\n${name}=${desktopMeta[name]}`\n    }\n    data += \"\\n\"\n    return Promise.resolve(data)\n  }\n}\n\nconst macToLinuxCategory: any = {\n  \"public.app-category.graphics-design\": \"Graphics\",\n  \"public.app-category.developer-tools\": \"Development\",\n  \"public.app-category.education\": \"Education\",\n  \"public.app-category.games\": \"Game\",\n  \"public.app-category.video\": \"Video;AudioVideo\",\n  \"public.app-category.utilities\": \"Utility\",\n  \"public.app-category.social-networking\": \"Network;Chat\",\n  \"public.app-category.finance\": \"Office;Finance\",\n}\n"]}