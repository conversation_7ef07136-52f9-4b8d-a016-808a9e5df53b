<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Voice-to-Text Dashboard</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 20px;
      background-color: #f5f5f5;
    }
    
    .container {
      max-width: 800px;
      margin: 0 auto;
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
      padding: 20px;
    }
    
    h1 {
      margin-top: 0;
      color: #333;
    }
    
    .tabs {
      display: flex;
      border-bottom: 1px solid #ddd;
      margin-bottom: 20px;
    }
    
    .tab {
      padding: 10px 20px;
      cursor: pointer;
      border-bottom: 2px solid transparent;
    }
    
    .tab.active {
      border-bottom: 2px solid #007bff;
      color: #007bff;
    }
    
    .tab-content {
      display: none;
    }
    
    .tab-content.active {
      display: block;
    }
    
    .shortcut-recorder {
      display: flex;
      align-items: center;
      margin-bottom: 20px;
    }
    
    input, button {
      padding: 8px 12px;
      margin-right: 10px;
    }
    
    button {
      background: #007bff;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
    }
    
    button:hover {
      background: #0069d9;
    }
    
    .history-item {
      padding: 10px;
      border-bottom: 1px solid #eee;
    }
    
    .login-form {
      max-width: 400px;
    }
    
    .form-group {
      margin-bottom: 15px;
    }
    
    label {
      display: block;
      margin-bottom: 5px;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>Voice-to-Text Dashboard</h1>
    
    <div class="tabs">
      <div class="tab active" data-tab="settings">Settings</div>
      <div class="tab" data-tab="history">History</div>
      <div class="tab" data-tab="account">Account</div>
    </div>
    
    <div class="tab-content active" id="settings">
      <h2>Shortcut Settings</h2>
      <div class="shortcut-recorder">
        <input type="text" id="shortcut-input" placeholder="Click 'Record' and press keys" readonly>
        <button id="record-shortcut-btn">Record</button>
        <button id="save-shortcut-btn">Save</button>
      </div>
    </div>
    
    <div class="tab-content" id="history">
      <h2>Transcription History</h2>
      <div id="history-list">
        <!-- History items will be added here dynamically -->
      </div>
    </div>
    
    <div class="tab-content" id="account">
      <h2>Account</h2>
      <div id="login-section">
        <div class="login-form">
          <div class="form-group">
            <label for="email">Email</label>
            <input type="email" id="email" placeholder="Enter your email">
          </div>
          <div class="form-group">
            <label for="password">Password</label>
            <input type="password" id="password" placeholder="Enter your password">
          </div>
          <button id="login-btn">Login</button>
        </div>
      </div>
      <div id="user-section" style="display: none;">
        <h3>Welcome, <span id="user-name"></span></h3>
        <button id="logout-btn">Logout</button>
      </div>
    </div>
  </div>
  
  <script src="dashboard.js"></script>
</body>
</html>