{"version": 3, "file": "vm.js", "sourceRoot": "", "sources": ["../../src/vm/vm.ts"], "names": [], "mappings": ";;;AAAA,+CAAqG;AAErG,6BAA4B;AAE5B,MAAa,SAAS;IACpB,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,GAAG,CAAA;IACjB,CAAC;IAED,IAAI,CAAC,IAAY,EAAE,IAAmB,EAAE,OAAyB,EAAE,eAAe,GAAG,IAAI;QACvF,OAAO,mBAAI,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,eAAe,CAAC,CAAA;IACnD,CAAC;IAED,KAAK,CAAC,IAAY,EAAE,IAAmB,EAAE,OAAsB,EAAE,YAAgC;QAC/F,OAAO,oBAAK,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,YAAY,CAAC,CAAA;IACjD,CAAC;IAED,QAAQ,CAAC,IAAY;QACnB,OAAO,IAAI,CAAA;IACb,CAAC;CACF;AAhBD,8BAgBC;AAEM,KAAK,UAAU,YAAY,CAAC,WAAwB;IACzD,MAAM,iBAAiB,GAAG,2CAAa,eAAe,EAAC,CAAA;IACvD,MAAM,MAAM,GAAG,CAAC,MAAM,iBAAiB,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAA;IACpH,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;QACvB,MAAM,IAAI,wCAAyB,CAAC,iFAAiF,CAAC,CAAA;KACvH;IAED,iCAAiC;IACjC,OAAO,IAAI,iBAAiB,CAAC,kBAAkB,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK,KAAK,SAAS,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK,KAAK,WAAW,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,CAAA;AACxJ,CAAC;AATD,oCASC", "sourcesContent": ["import { DebugLogger, exec, ExtraSpawnOptions, InvalidConfigurationError, spawn } from \"builder-util\"\nimport { ExecFileOptions, SpawnOptions } from \"child_process\"\nimport * as path from \"path\"\n\nexport class VmManager {\n  get pathSep(): string {\n    return path.sep\n  }\n\n  exec(file: string, args: Array<string>, options?: ExecFileOptions, isLogOutIfDebug = true): Promise<string> {\n    return exec(file, args, options, isLogOutIfDebug)\n  }\n\n  spawn(file: string, args: Array<string>, options?: SpawnOptions, extraOptions?: ExtraSpawnOptions): Promise<any> {\n    return spawn(file, args, options, extraOptions)\n  }\n\n  toVmFile(file: string): string {\n    return file\n  }\n}\n\nexport async function getWindowsVm(debugLogger: DebugLogger): Promise<VmManager> {\n  const parallelsVmModule = await import(\"./ParallelsVm\")\n  const vmList = (await parallelsVmModule.parseVmList(debugLogger)).filter(it => [\"win-10\", \"win-11\"].includes(it.os))\n  if (vmList.length === 0) {\n    throw new InvalidConfigurationError(\"Cannot find suitable Parallels Desktop virtual machine (Windows 10 is required)\")\n  }\n\n  // prefer running or suspended vm\n  return new parallelsVmModule.ParallelsVmManager(vmList.find(it => it.state === \"running\") || vmList.find(it => it.state === \"suspended\") || vmList[0])\n}\n"]}