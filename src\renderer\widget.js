const { ipc<PERSON><PERSON><PERSON> } = require('electron');

const widgetIcon = document.getElementById('widget-icon');
const tooltip = document.getElementById('tooltip');
let isRecording = false;

// Show tooltip on hover
widgetIcon.addEventListener('mouseenter', () => {
  tooltip.style.display = 'block';
});

widgetIcon.addEventListener('mouseleave', () => {
  tooltip.style.display = 'none';
});

// Listen for recording start/stop events from main process
ipcRenderer.on('recording-started', () => {
  widgetIcon.style.backgroundColor = 'red';
  widgetIcon.style.borderRadius = '50%';
  tooltip.innerText = 'Recording... Release shortcut to stop';
  isRecording = true;
});

ipcRenderer.on('recording-stopped', () => {
  widgetIcon.style.backgroundColor = 'transparent';
  tooltip.innerText = 'Press shortcut to record';
  isRecording = false;
});
