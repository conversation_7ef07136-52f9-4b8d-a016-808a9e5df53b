const { ipcRenderer } = require('electron');

const widgetIcon = document.getElementById('widget-icon');
const tooltip = document.getElementById('tooltip');
const micIcon = document.querySelector('.mic-icon');
let isRecording = false;
let mediaRecorder = null;
let audioChunks = [];

// Show tooltip on hover
widgetIcon.addEventListener('mouseenter', () => {
  tooltip.style.display = 'block';
});

widgetIcon.addEventListener('mouseleave', () => {
  tooltip.style.display = 'none';
});

// Listen for recording start/stop events from main process
ipcRenderer.on('recording-started', () => {
  micIcon.style.backgroundColor = '#ff4444';
  micIcon.style.animation = 'pulse 1s infinite';
  tooltip.innerText = 'Recording... Release shortcut to stop';
  isRecording = true;
});

ipcRenderer.on('recording-stopped', () => {
  micIcon.style.backgroundColor = '#4CAF50';
  micIcon.style.animation = 'none';
  tooltip.innerText = 'Press shortcut to record';
  isRecording = false;
});

// Handle start recording from shortcut
ipcRenderer.on('start-recording-from-shortcut', async () => {
  try {
    // Stop any existing recording
    if (mediaRecorder && mediaRecorder.state === 'recording') {
      mediaRecorder.stop();
    }

    // Clear previous audio chunks
    audioChunks = [];

    // Request microphone access
    const stream = await navigator.mediaDevices.getUserMedia({
      audio: {
        echoCancellation: true,
        noiseSuppression: true,
        autoGainControl: true
      }
    });

    // Create media recorder with better audio quality
    mediaRecorder = new MediaRecorder(stream, {
      mimeType: 'audio/webm;codecs=opus',
      audioBitsPerSecond: 128000
    });

    // Collect audio data
    mediaRecorder.ondataavailable = (event) => {
      if (event.data.size > 0) {
        audioChunks.push(event.data);
      }
    };

    // Handle recording stop
    mediaRecorder.onstop = async () => {
      try {
        // Show processing indicator
        micIcon.style.backgroundColor = '#FFA500'; // Orange
        tooltip.innerText = 'Processing audio...';

        // Create audio blob
        const audioBlob = new Blob(audioChunks, { type: 'audio/webm' });

        // Convert to base64
        const reader = new FileReader();
        reader.readAsDataURL(audioBlob);
        reader.onloadend = async () => {
          const base64Audio = reader.result.split(',')[1]; // Remove data URL prefix

          // Send audio data to main process for transcription
          const result = await ipcRenderer.invoke('transcribe-audio', {
            base64Audio: base64Audio,
            mimeType: audioBlob.type,
            size: audioBlob.size
          });

          console.log('Transcription completed');

          // Notify main process that recording is completed
          ipcRenderer.send('recording-completed');

          // Hide widget after transcription
          setTimeout(() => {
            ipcRenderer.send('hide-widget');
          }, 1000);
        };

        // Stop all tracks
        stream.getTracks().forEach(track => track.stop());
      } catch (error) {
        console.error('Error processing recording:', error);
        tooltip.innerText = 'Error processing audio';
        micIcon.style.backgroundColor = '#4CAF50'; // Reset to green
      }
    };

    // Start recording with data collection every 1 second
    mediaRecorder.start(1000);
    console.log('Recording started');
  } catch (error) {
    console.error('Error starting recording:', error);
    tooltip.innerText = 'Microphone access denied';
    micIcon.style.backgroundColor = '#FF0000'; // Red to indicate error
  }
});

// Handle stop recording from shortcut
ipcRenderer.on('stop-recording-from-shortcut', () => {
  if (mediaRecorder && mediaRecorder.state === 'recording') {
    mediaRecorder.stop();
    console.log('Recording stopped');
  }
});

// Add CSS for pulse animation
const style = document.createElement('style');
style.textContent = `
  @keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
  }
`;
document.head.appendChild(style);
