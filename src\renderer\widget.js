// Use the electronAPI exposed by preload
const widgetIcon = document.getElementById('widget-icon');
const tooltip = document.getElementById('tooltip');
const micIcon = document.querySelector('.mic-icon');
let isRecording = false;
let mediaRecorder = null;
let audioChunks = [];

// Check if electronAPI is available
if (!window.electronAPI) {
  console.error('❌ electronAPI not available - preload script may not be working');
}

// Show tooltip on hover
widgetIcon.addEventListener('mouseenter', () => {
  tooltip.style.display = 'block';
});

widgetIcon.addEventListener('mouseleave', () => {
  tooltip.style.display = 'none';
});

// Listen for recording start/stop events from main process
ipcRenderer.on('recording-started', () => {
  console.log('🔴 Widget: Recording started');
  micIcon.style.backgroundColor = '#ff4444';
  micIcon.style.animation = 'pulse 1s infinite';
  tooltip.innerText = 'Recording... Release shortcut to stop';
  isRecording = true;
});

ipcRenderer.on('recording-stopped', () => {
  console.log('⚪ Widget: Recording stopped');
  micIcon.style.backgroundColor = '#4CAF50';
  micIcon.style.animation = 'none';
  tooltip.innerText = 'Press shortcut to record';
  isRecording = false;
});

// Handle start recording from shortcut
ipcRenderer.on('start-recording-from-shortcut', async () => {
  console.log('🎤 Widget: Starting audio recording...');
  try {
    // Stop any existing recording
    if (mediaRecorder && mediaRecorder.state === 'recording') {
      console.log('⚠️  Widget: Stopping existing recording');
      mediaRecorder.stop();
    }

    // Clear previous audio chunks
    audioChunks = [];

    // Request microphone access
    console.log('🎧 Widget: Requesting microphone access...');
    const stream = await navigator.mediaDevices.getUserMedia({
      audio: {
        echoCancellation: true,
        noiseSuppression: true,
        autoGainControl: true
      }
    });

    // Create media recorder with better audio quality
    mediaRecorder = new MediaRecorder(stream, {
      mimeType: 'audio/webm;codecs=opus',
      audioBitsPerSecond: 128000
    });

    console.log('🎛️  Widget: MediaRecorder created');

    // Collect audio data
    mediaRecorder.ondataavailable = (event) => {
      console.log('📊 Widget: Audio data available, size:', event.data.size);
      if (event.data.size > 0) {
        audioChunks.push(event.data);
      }
    };

    // Handle recording stop
    mediaRecorder.onstop = async () => {
      console.log('⏹️  Widget: MediaRecorder stopped');
      console.log('   - Audio chunks collected:', audioChunks.length);

      try {
        // Show processing indicator
        micIcon.style.backgroundColor = '#FFA500'; // Orange
        tooltip.innerText = 'Processing audio...';

        // Make sure we have audio data
        if (audioChunks.length === 0) {
          console.error('❌ Widget: No audio data collected!');
          micIcon.style.backgroundColor = '#4CAF50'; // Reset to green
          return;
        }

        // Create audio blob
        const audioBlob = new Blob(audioChunks, { type: 'audio/webm' });
        console.log('📦 Widget: Audio blob created, size:', audioBlob.size);

        // Convert to base64
        const reader = new FileReader();
        reader.readAsDataURL(audioBlob);
        reader.onloadend = async () => {
          const base64Audio = reader.result.split(',')[1]; // Remove data URL prefix

          console.log('📤 Widget: Sending audio for transcription...');
          console.log('   - Audio size:', audioBlob.size, 'bytes');
          console.log('   - MIME type:', audioBlob.type);
          console.log('   - Base64 length:', base64Audio.length);

          // Send audio data to main process for transcription
          const result = await ipcRenderer.invoke('transcribe-audio', {
            base64Audio: base64Audio,
            mimeType: audioBlob.type,
            size: audioBlob.size
          });

          console.log('✅ Widget: Transcription completed:', result);

          // Notify main process that recording is completed
          ipcRenderer.send('recording-completed');

          // Hide widget after transcription
          setTimeout(() => {
            ipcRenderer.send('hide-widget');
          }, 1000);
        };

        // Stop all tracks
        stream.getTracks().forEach(track => track.stop());
      } catch (error) {
        console.error('Error processing recording:', error);
        tooltip.innerText = 'Error processing audio';
        micIcon.style.backgroundColor = '#4CAF50'; // Reset to green
      }
    };

    // Start recording with data collection every 100ms for better responsiveness
    mediaRecorder.start(100);
    console.log('✅ Widget: Recording started successfully');
  } catch (error) {
    console.error('Error starting recording:', error);
    tooltip.innerText = 'Microphone access denied';
    micIcon.style.backgroundColor = '#FF0000'; // Red to indicate error
  }
});

// Handle stop recording from shortcut
ipcRenderer.on('stop-recording-from-shortcut', () => {
  console.log('🛑 Widget: Stop recording command received');
  if (mediaRecorder && mediaRecorder.state === 'recording') {
    console.log('⏹️  Widget: Stopping MediaRecorder...');
    mediaRecorder.stop();
  } else {
    console.log('⚠️  Widget: MediaRecorder not recording or not available');
    console.log('   - MediaRecorder state:', mediaRecorder ? mediaRecorder.state : 'null');
  }
});

// Add CSS for pulse animation
const style = document.createElement('style');
style.textContent = `
  @keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
  }
`;
document.head.appendChild(style);
