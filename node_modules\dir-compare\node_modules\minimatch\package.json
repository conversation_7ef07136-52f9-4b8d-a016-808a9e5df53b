{"author": "<PERSON> <<EMAIL>> (http://blog.izs.me)", "name": "minimatch", "description": "a glob matcher in javascript", "version": "3.0.4", "repository": {"type": "git", "url": "git://github.com/isaacs/minimatch.git"}, "main": "minimatch.js", "scripts": {"test": "tap test/*.js --cov", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "engines": {"node": "*"}, "dependencies": {"brace-expansion": "^1.1.7"}, "devDependencies": {"tap": "^10.3.2"}, "license": "ISC", "files": ["minimatch.js"]}