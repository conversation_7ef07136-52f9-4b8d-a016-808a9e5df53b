{"version": 3, "file": "AppFileWalker.js", "sourceRoot": "", "sources": ["../../src/util/AppFileWalker.ts"], "names": [], "mappings": ";;;AACA,uCAAgD;AAGhD,6BAA4B;AAE5B,MAAM,gCAAgC,GAAG,GAAG,IAAI,CAAC,GAAG,cAAc,CAAA;AAElE,SAAS,mBAAmB,CAAC,OAAoB;IAC/C,IAAI,CAAC,OAAO,CAAC,uBAAuB,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,IAAI,OAAO,CAAC,kBAAkB,EAAE,CAAC,EAAE;QAC3F,OAAO,CAAC,cAAc,CAAC,MAAM,CAAC,CAAA;KAC/B;IACD,OAAO,OAAO,CAAA;AAChB,CAAC;AAED,MAAsB,cAAc;IAGlC,YAAyC,OAAoB,EAAW,MAAqB,EAAqB,QAAkB;QAA3F,YAAO,GAAP,OAAO,CAAa;QAAW,WAAM,GAAN,MAAM,CAAe;QAAqB,aAAQ,GAAR,QAAQ,CAAU;QAF3H,aAAQ,GAAG,IAAI,GAAG,EAAiB,CAAA;IAE2F,CAAC;IAE9H,UAAU,CAAC,IAAY,EAAE,MAAc,EAAE,QAAe;QAChE,IAAI,CAAC,QAAQ,CAAC,cAAc,EAAE,EAAE;YAC9B,OAAO,IAAI,CAAA;SACZ;QAED,OAAO,mBAAQ,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,UAAU,EAAO,EAAE;YAC7C,+HAA+H;YAC/H,OAAO,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,IAAI,EAAE,MAAM,EAAE,UAAU,CAAC,CAAA;QAC/D,CAAC,CAAC,CAAA;IACJ,CAAC;IAEO,aAAa,CAAC,QAAe,EAAE,IAAY,EAAE,MAAc,EAAE,UAAkB;QACrF,MAAM,kBAAkB,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,UAAU,CAAC,CAAA;QAC3D,MAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,kBAAkB,CAAC,CAAA;QACjE,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE;YACzB,uGAAuG;YACvG,OAAO,eAAI,CAAC,kBAAkB,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,EAAE;gBACpD,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,EAAE,cAAc,CAAC,CAAA;gBACvC,OAAO,cAAc,CAAA;YACvB,CAAC,CAAC,CAAA;SACH;aAAM;YACL,MAAM,CAAC,GAAG,QAAe,CAAA;YACzB,CAAC,CAAC,YAAY,GAAG,IAAI,CAAA;YACrB,CAAC,CAAC,kBAAkB,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,kBAAkB,CAAC,CAAA;SACjE;QACD,OAAO,IAAI,CAAA;IACb,CAAC;CACF;AAhCD,wCAgCC;AAED,SAAS,eAAe,CAAC,OAAoB,EAAE,QAAkB;IAC/D,IAAI,QAAQ,CAAC,+BAA+B,EAAE;QAC5C,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,YAAY,EAAE,CAAA;KACzD;IAED,MAAM,iBAAiB,GAAW,CAAC,IAAI,EAAE,QAAQ,EAAE,EAAE;QACnD,OAAO,CAAC,CAAC,QAAQ,CAAC,WAAW,EAAE,IAAI,IAAI,CAAC,QAAQ,CAAC,gCAAgC,CAAC,CAAC,CAAA;IACrF,CAAC,CAAA;IAED,IAAI,OAAO,CAAC,OAAO,EAAE,EAAE;QACrB,OAAO,iBAAiB,CAAA;KACzB;IAED,MAAM,MAAM,GAAG,OAAO,CAAC,YAAY,EAAE,CAAA;IACrC,OAAO,CAAC,IAAI,EAAE,QAAQ,EAAE,EAAE;QACxB,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,QAAQ,CAAC,EAAE;YACtC,OAAO,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,qBAAqB,CAAA;SAC/C;QACD,OAAO,MAAM,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAA;IAC/B,CAAC,CAAA;AACH,CAAC;AAED,gBAAgB;AAChB,MAAa,aAAc,SAAQ,cAAc;IAE/C,YAAY,OAAoB,EAAE,QAAkB;QAClD,KAAK,CAAC,mBAAmB,CAAC,OAAO,CAAC,EAAE,eAAe,CAAC,OAAO,EAAE,QAAQ,CAAC,EAAE,QAAQ,CAAC,CAAA;QACjF,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC,YAAY,EAAE,CAAA;IAC7C,CAAC;IAED,qCAAqC;IACrC,6DAA6D;IAC7D,OAAO,CAAC,IAAY,EAAE,QAAe,EAAE,MAAc,EAAE,YAA2B;QAChF,IAAI,QAAQ,CAAC,WAAW,EAAE,EAAE;YAC1B,oEAAoE;YACpE,kDAAkD;YAClD,4EAA4E;YAC5E,IAAI,IAAI,CAAC,QAAQ,CAAC,gCAAgC,CAAC,EAAE;gBACnD,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,qBAAqB,EAAE;oBAC/C,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAA;oBACxD,IAAI,CAAC,aAAa,EAAE;wBAClB,gBAAgB;wBAChB,OAAO,KAAK,CAAA;qBACb;iBACF;aACF;SACF;aAAM;YACL,oDAAoD;YACpD,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAA;SAClC;QAED,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAA;IAChD,CAAC;CACF;AA9BD,sCA8BC", "sourcesContent": ["import { Filter, FileConsumer } from \"builder-util/out/fs\"\nimport { readlink, stat, Stats } from \"fs-extra\"\nimport { FileMatcher } from \"../fileMatcher\"\nimport { Packager } from \"../packager\"\nimport * as path from \"path\"\n\nconst nodeModulesSystemDependentSuffix = `${path.sep}node_modules`\n\nfunction addAllPatternIfNeed(matcher: FileMatcher) {\n  if (!matcher.isSpecifiedAsEmptyArray && (matcher.isEmpty() || matcher.containsOnlyIgnore())) {\n    matcher.prependPattern(\"**/*\")\n  }\n  return matcher\n}\n\nexport abstract class FileCopyHelper {\n  readonly metadata = new Map<string, Stats>()\n\n  protected constructor(protected readonly matcher: FileMatcher, readonly filter: Filter | null, protected readonly packager: Packager) {}\n\n  protected handleFile(file: string, parent: string, fileStat: Stats): Promise<Stats | null> | null {\n    if (!fileStat.isSymbolicLink()) {\n      return null\n    }\n\n    return readlink(file).then((linkTarget): any => {\n      // http://unix.stackexchange.com/questions/105637/is-symlinks-target-relative-to-the-destinations-parent-directory-and-if-so-wh\n      return this.handleSymlink(fileStat, file, parent, linkTarget)\n    })\n  }\n\n  private handleSymlink(fileStat: Stats, file: string, parent: string, linkTarget: string): Promise<Stats> | null {\n    const resolvedLinkTarget = path.resolve(parent, linkTarget)\n    const link = path.relative(this.matcher.from, resolvedLinkTarget)\n    if (link.startsWith(\"..\")) {\n      // outside of project, linked module (https://github.com/electron-userland/electron-builder/issues/675)\n      return stat(resolvedLinkTarget).then(targetFileStat => {\n        this.metadata.set(file, targetFileStat)\n        return targetFileStat\n      })\n    } else {\n      const s = fileStat as any\n      s.relativeLink = link\n      s.linkRelativeToFile = path.relative(parent, resolvedLinkTarget)\n    }\n    return null\n  }\n}\n\nfunction createAppFilter(matcher: FileMatcher, packager: Packager): Filter | null {\n  if (packager.areNodeModulesHandledExternally) {\n    return matcher.isEmpty() ? null : matcher.createFilter()\n  }\n\n  const nodeModulesFilter: Filter = (file, fileStat) => {\n    return !(fileStat.isDirectory() && file.endsWith(nodeModulesSystemDependentSuffix))\n  }\n\n  if (matcher.isEmpty()) {\n    return nodeModulesFilter\n  }\n\n  const filter = matcher.createFilter()\n  return (file, fileStat) => {\n    if (!nodeModulesFilter(file, fileStat)) {\n      return !!packager.config.includeSubNodeModules\n    }\n    return filter(file, fileStat)\n  }\n}\n\n/** @internal */\nexport class AppFileWalker extends FileCopyHelper implements FileConsumer {\n  readonly matcherFilter: any\n  constructor(matcher: FileMatcher, packager: Packager) {\n    super(addAllPatternIfNeed(matcher), createAppFilter(matcher, packager), packager)\n    this.matcherFilter = matcher.createFilter()\n  }\n\n  // noinspection JSUnusedGlobalSymbols\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  consume(file: string, fileStat: Stats, parent: string, siblingNames: Array<string>): any {\n    if (fileStat.isDirectory()) {\n      // https://github.com/electron-userland/electron-builder/issues/1539\n      // but do not filter if we inside node_modules dir\n      // update: solution disabled, node module resolver should support such setup\n      if (file.endsWith(nodeModulesSystemDependentSuffix)) {\n        if (!this.packager.config.includeSubNodeModules) {\n          const matchesFilter = this.matcherFilter(file, fileStat)\n          if (!matchesFilter) {\n            // Skip the file\n            return false\n          }\n        }\n      }\n    } else {\n      // save memory - no need to store stat for directory\n      this.metadata.set(file, fileStat)\n    }\n\n    return this.handleFile(file, parent, fileStat)\n  }\n}\n"]}