#!/usr/bin/env node
'use strict';
var copyfiles = require('./index');
var args = require('yargs');
var path = require('path');
args.alias('u', 'up')
  .describe('u', 'slice a path off the bottom of the paths')
  .number('u')
if (path.basename(args.argv.$0) === 'copyup') {
  args.default('u', '1');
}
args.alias('a', 'all')
  .describe('a', 'include files & directories begining with a dot (.)')
  .boolean('a')
  .alias('f','flat')
  .describe('f', 'flatten the output')
  .boolean('f')
  .alias('e', 'exclude')
  .describe('e', 'pattern or glob to exclude (may be passed multiple times)')
  .alias('E', 'error')
  .boolean('E')
  .describe('E', 'throw error if nothing is coppied')
  .alias('V', 'verbose')
  .boolean('V')
  .describe('V', 'print more information to console')
  .alias('s','soft')
  .boolean('s')
  .describe('s', 'do not overwrite destination files if they exist')
  .alias('F', 'follow')
  .boolean('F')
  .describe('F', 'follow symbolink links')
  .usage('$0 [options] inFile [more files ...] outDirectory')
  .help()
  .alias('v', 'version')
  .alias('h', 'help');
var argv = args.argv;
if (argv.flat) {
  argv.up = true;
}
copyfiles(argv._, argv, function (err) {
  if (err) {
    console.error(err);
    process.exit(1);
  } else {
    process.exit(0);
  }
});
