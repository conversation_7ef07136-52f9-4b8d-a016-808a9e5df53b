{"name": "dir-compare", "version": "2.4.0", "description": "Node JS directory compare", "main": "src/index.js", "types": "src/index.d.ts", "repository": {"type": "git", "url": "https://github.com/gliviu/dir-compare"}, "keywords": ["compare", "directory", "folder"], "files": ["src"], "scripts": {"clean": "rm -rf build && rm -rf .nyc_output && rm -rf coverage", "copydeps": "copyfiles \"test/expected/**\" test/testdir.tar \"test/extended/res/**\" package.json build", "build": "tsc && npm run copydeps", "lint": "tslint -p tsconfig.json", "pretest": "npm install && npm run build", "test": "node build/test/runTests.js", "extest": "npm run pretest && ./test/extended/init.sh && test/extended/runall.sh", "coverage": "npx nyc --exclude \"build/test/**\" --reporter=lcov npm test && npx nyc report", "toc": "npx markdown-toc README.md; echo \n", "docs": "typedoc --includeVersion --includeDeclarations --excludeExternals --theme minimal --mode file --readme none --gitRevision master --toc compare,compareSync,fileCompareHandlers,Options,Result --out docs ./src/index.d.ts"}, "dependencies": {"buffer-equal": "1.0.0", "colors": "1.0.3", "commander": "2.9.0", "minimatch": "3.0.4"}, "devDependencies": {"@types/node": "^12.11.7", "copyfiles": "^1.2.0", "memory-streams": "0.1.0", "semver": "5.6.0", "shelljs": "0.3.0", "tar-fs": "2.1.1", "temp": "0.9.0", "ts-node": "^8.5.4", "tslint": "^5.20.0", "tslint-config-prettier": "^1.13.0", "typedoc": "0.19.2", "typescript": "^3.7.4"}, "bin": {"dircompare": "src/cli/dircompare.js"}, "author": "<PERSON><PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/gliviu/dir-compare/issues"}}