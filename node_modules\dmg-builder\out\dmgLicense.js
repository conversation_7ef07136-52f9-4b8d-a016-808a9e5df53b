"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.addLicenseToDmg = void 0;
const builder_util_1 = require("builder-util");
const js_yaml_1 = require("js-yaml");
const license_1 = require("app-builder-lib/out/util/license");
const fs_extra_1 = require("fs-extra");
const licenseButtons_1 = require("./licenseButtons");
const dmg_license_1 = require("dmg-license");
async function addLicenseToDmg(packager, dmgPath) {
    const licenseFiles = await license_1.getLicenseFiles(packager);
    if (licenseFiles.length === 0) {
        return null;
    }
    const licenseButtonFiles = await licenseButtons_1.getLicenseButtonsFile(packager);
    packager.debugLogger.add("dmg.licenseFiles", licenseFiles);
    packager.debugLogger.add("dmg.licenseButtons", licenseButtonFiles);
    const jsonFile = {
        $schema: "https://github.com/argv-minus-one/dmg-license/raw/master/schema.json",
        // defaultLang: '',
        body: [],
        labels: [],
    };
    for (const file of licenseFiles) {
        jsonFile.body.push({
            file: file.file,
            lang: file.langWithRegion.replace("_", "-"),
        });
    }
    for (const button of licenseButtonFiles) {
        const filepath = button.file;
        const label = filepath.endsWith(".yml") ? js_yaml_1.load(await fs_extra_1.readFile(filepath, "utf-8")) : await fs_extra_1.readJson(filepath);
        if (label.description) {
            // to support original button file format
            label.message = label.description;
            delete label.description;
        }
        jsonFile.labels.push(Object.assign({
            lang: button.langWithRegion.replace("_", "-"),
        }, label));
    }
    await dmg_license_1.dmgLicenseFromJSON(dmgPath, jsonFile, {
        onNonFatalError: builder_util_1.log.warn.bind(builder_util_1.log),
    });
    return jsonFile;
}
exports.addLicenseToDmg = addLicenseToDmg;
//# sourceMappingURL=dmgLicense.js.map