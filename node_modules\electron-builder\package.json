{"name": "electron-builder", "description": "A complete solution to package and build a ready for distribution Electron app for MacOS, Windows and Linux with “auto update” support out of the box", "version": "23.6.0", "main": "out/index.js", "files": ["out"], "bin": {"electron-builder": "./cli.js", "install-app-deps": "./install-app-deps.js"}, "repository": {"type": "git", "url": "git+https://github.com/electron-userland/electron-builder.git", "directory": "packages/electron-builder"}, "engines": {"node": ">=14.0.0"}, "keywords": ["electron", "builder", "build", "installer", "install", "packager", "pack", "nsis", "app", "dmg", "pkg", "msi", "exe", "setup", "Windows", "OS X", "MacOS", "<PERSON>", "appx", "snap", "flatpak", "portable"], "author": "<PERSON>", "contributors": ["<PERSON>"], "license": "MIT", "bugs": "https://github.com/electron-userland/electron-builder/issues", "homepage": "https://github.com/electron-userland/electron-builder", "dependencies": {"@types/yargs": "^17.0.1", "app-builder-lib": "23.6.0", "builder-util": "23.6.0", "builder-util-runtime": "9.1.1", "chalk": "^4.1.1", "dmg-builder": "23.6.0", "fs-extra": "^10.0.0", "is-ci": "^3.0.0", "lazy-val": "^1.0.5", "read-config-file": "6.2.0", "simple-update-notifier": "^1.0.7", "yargs": "^17.5.1"}, "devDependencies": {"@types/fs-extra": "9.0.13", "@types/is-ci": "3.0.0"}, "typings": "./out/index.d.ts", "publishConfig": {"tag": "next"}}