const { ipc<PERSON><PERSON><PERSON> } = require('electron');

// Tab switching
const tabs = document.querySelectorAll('.tab');
const tabContents = document.querySelectorAll('.tab-content');

tabs.forEach(tab => {
  tab.addEventListener('click', () => {
    const tabId = tab.getAttribute('data-tab');
    
    // Update active tab
    tabs.forEach(t => t.classList.remove('active'));
    tab.classList.add('active');
    
    // Show active content
    tabContents.forEach(content => {
      content.classList.remove('active');
      if (content.id === tabId) {
        content.classList.add('active');
      }
    });
  });
});

// Shortcut recording
const shortcutInput = document.getElementById('shortcut-input');
const recordShortcutBtn = document.getElementById('record-shortcut-btn');
const saveShortcutBtn = document.getElementById('save-shortcut-btn');
let isRecordingShortcut = false;
let currentShortcutKeys = [];

// Load current shortcut
ipcRenderer.invoke('get-settings').then(settings => {
  shortcutInput.value = settings.globalShortcut || 'Control+Shift+Space';
});

recordShortcutBtn.addEventListener('click', () => {
  if (isRecordingShortcut) {
    stopRecordingShortcut();
  } else {
    startRecordingShortcut();
  }
});

function startRecordingShortcut() {
  isRecordingShortcut = true;
  recordShortcutBtn.textContent = 'Stop';
  shortcutInput.value = 'Press keys...';
  currentShortcutKeys = [];
  
  // Focus the input to capture key events
  shortcutInput.focus();
}

function stopRecordingShortcut() {
  isRecordingShortcut = false;
  recordShortcutBtn.textContent = 'Record';
  shortcutInput.blur();
}

// Handle key events for shortcut recording
document.addEventListener('keydown', (e) => {
  if (!isRecordingShortcut) return;
  
  e.preventDefault();
  
  const key = e.key;
  if (!currentShortcutKeys.includes(key) && 
      !['Control', 'Alt', 'Shift', 'Meta'].includes(key)) {
    currentShortcutKeys.push(key);
  }
  
  if (e.ctrlKey && !currentShortcutKeys.includes('Control')) {
    currentShortcutKeys.unshift('Control');
  }
  
  if (e.altKey && !currentShortcutKeys.includes('Alt')) {
    currentShortcutKeys.unshift('Alt');
  }
  
  if (e.shiftKey && !currentShortcutKeys.includes('Shift')) {
    currentShortcutKeys.unshift('Shift');
  }
  
  if (e.metaKey && !currentShortcutKeys.includes('Meta')) {
    currentShortcutKeys.unshift('Meta');
  }
  
  shortcutInput.value = currentShortcutKeys.join('+');
});

saveShortcutBtn.addEventListener('click', () => {
  const shortcut = shortcutInput.value;
  if (shortcut && shortcut !== 'Press keys...') {
    ipcRenderer.invoke('save-settings', { globalShortcut: shortcut })
      .then(result => {
        if (result.success) {
          alert('Shortcut saved successfully!');
        } else {
          alert('Failed to save shortcut.');
        }
      });
  }
});

// History tab
function loadHistory() {
  ipcRenderer.invoke('get-history').then(history => {
    const historyList = document.getElementById('history-list');
    historyList.innerHTML = '';
    
    if (history.length === 0) {
      historyList.innerHTML = '<p>No transcription history yet.</p>';
      return;
    }
    
    history.forEach(item => {
      const historyItem = document.createElement('div');
      historyItem.className = 'history-item';
      historyItem.innerHTML = `
        <p><strong>${new Date(item.timestamp).toLocaleString()}</strong></p>
        <p>${item.text}</p>
      `;
      historyList.appendChild(historyItem);
    });
  });
}

// Load history when tab is clicked
document.querySelector('[data-tab="history"]').addEventListener('click', loadHistory);

// Login functionality
const loginBtn = document.getElementById('login-btn');
const logoutBtn = document.getElementById('logout-btn');
const emailInput = document.getElementById('email');
const passwordInput = document.getElementById('password');
const loginSection = document.getElementById('login-section');
const userSection = document.getElementById('user-section');
const userName = document.getElementById('user-name');

loginBtn.addEventListener('click', () => {
  const email = emailInput.value;
  const password = passwordInput.value;
  
  if (!email || !password) {
    alert('Please enter both email and password');
    return;
  }
  
  ipcRenderer.invoke('login', { email, password })
    .then(result => {
      if (result.success) {
        loginSection.style.display = 'none';
        userSection.style.display = 'block';
        userName.textContent = result.user.name || email;
      } else {
        alert('Login failed: ' + result.message);
      }
    });
});

logoutBtn.addEventListener('click', () => {
  ipcRenderer.invoke('logout')
    .then(() => {
      loginSection.style.display = 'block';
      userSection.style.display = 'none';
      emailInput.value = '';
      passwordInput.value = '';
    });
});

// Check if user is already logged in
ipcRenderer.invoke('check-auth')
  .then(result => {
    if (result.authenticated) {
      loginSection.style.display = 'none';
      userSection.style.display = 'block';
      userName.textContent = result.user.name || result.user.email;
    }
  });