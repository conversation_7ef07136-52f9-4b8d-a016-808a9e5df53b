const { ipc<PERSON><PERSON><PERSON> } = require('electron');

const widgetIcon = document.getElementById('widget-icon');
const tooltip = document.getElementById('tooltip');
const recordingAnimation = document.getElementById('recording-animation');
let isRecording = false;

// Show tooltip on hover
widgetIcon.addEventListener('mouseenter', () => {
  tooltip.style.display = 'block';
});

widgetIcon.addEventListener('mouseleave', () => {
  tooltip.style.display = 'none';
});

// Listen for recording start/stop events from main process
ipcRenderer.on('recording-started', () => {
  recordingAnimation.style.display = 'block';
  tooltip.innerText = 'Recording... Release shortcut to stop';
  isRecording = true;
});

ipcRenderer.on('recording-stopped', () => {
  recordingAnimation.style.display = 'none';
  tooltip.innerText = 'Press shortcut to record';
  isRecording = false;
});
