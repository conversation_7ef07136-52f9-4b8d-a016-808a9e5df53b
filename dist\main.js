"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const electron_1 = require("electron");
const path = __importStar(require("path"));
const dotenv = __importStar(require("dotenv"));
const https = __importStar(require("https"));
const fs = __importStar(require("fs"));
const os = __importStar(require("os"));
const child_process_1 = require("child_process");
const form_data_1 = __importDefault(require("form-data"));
const electron_store_1 = __importDefault(require("electron-store"));
// Add these variables
let tray = null;
let dashboardWindow = null;
// Create dashboard window function
function createDashboardWindow() {
    if (dashboardWindow) {
        dashboardWindow.focus();
        return;
    }
    dashboardWindow = new electron_1.BrowserWindow({
        width: 800,
        height: 600,
        webPreferences: {
            preload: path.join(__dirname, 'preload.js'),
            nodeIntegration: true,
            contextIsolation: true
        }
    });
    dashboardWindow.loadFile(path.join(__dirname, 'renderer', 'dashboard.html'));
    dashboardWindow.on('closed', () => {
        dashboardWindow = null;
    });
}
// Create system tray
function createTray() {
    try {
        // Create a simple icon using nativeImage
        const icon = electron_1.nativeImage.createEmpty();
        tray = new electron_1.Tray(icon);
    }
    catch (error) {
        console.log('⚠️  Could not create system tray:', error);
        return; // Skip tray creation if it fails
    }
    const contextMenu = electron_1.Menu.buildFromTemplate([
        {
            label: 'Settings',
            click: () => {
                createDashboardWindow();
            }
        },
        { type: 'separator' },
        {
            label: 'Quit',
            click: () => {
                electron_1.app.quit();
            }
        }
    ]);
    tray.setToolTip('Voice-to-Text Widget');
    tray.setContextMenu(contextMenu);
    // Double-click opens dashboard
    tray.on('double-click', () => {
        createDashboardWindow();
    });
}
dotenv.config();
console.log('🚀 Voice-to-Text Widget starting...');
const OPENAI_API_KEY = process.env.OPENAI_API_KEY;
console.log('🔑 OpenAI API Key loaded:', OPENAI_API_KEY ? 'Yes' : 'No');
// Initialize settings store
const store = new electron_store_1.default({
    defaults: {
        globalShortcut: 'Control+`' // Control + backtick is easier to press
    }
});
let mainWindow = null;
let currentShortcut = store.get('globalShortcut', 'Control+`');
let isRecordingActive = false;
let shortcutPressed = false;
let releaseTimer = null;
function createWindow() {
    const { screen } = require('electron');
    const primaryDisplay = screen.getPrimaryDisplay();
    const { width: screenWidth } = primaryDisplay.workAreaSize;
    const windowWidth = 120;
    const windowHeight = 120;
    mainWindow = new electron_1.BrowserWindow({
        width: windowWidth,
        height: windowHeight,
        x: Math.round((screenWidth - windowWidth) / 2), // Center horizontally
        y: 50, // Position at top of screen
        frame: false,
        transparent: true,
        focusable: false,
        alwaysOnTop: true,
        skipTaskbar: true,
        resizable: false,
        movable: true,
        webPreferences: {
            preload: path.join(__dirname, 'preload.js'),
            nodeIntegration: true,
            contextIsolation: true
        }
    });
    // Load the widget HTML file
    mainWindow.loadFile(path.join(__dirname, 'renderer', 'widget.html'));
    // Show widget on startup at top center of screen
    mainWindow.show();
}
function handleShortcutPress() {
    if (!mainWindow || isRecordingActive)
        return; // Prevent multiple triggers
    console.log('🎙️  Starting recording...');
    // Set flags
    shortcutPressed = true;
    isRecordingActive = true;
    // Notify widget to show recording animation
    mainWindow.webContents.send('recording-started');
    // Start recording
    mainWindow.webContents.send('start-recording-from-shortcut');
    // Set up a safety timer (30 seconds max recording)
    if (releaseTimer) {
        clearTimeout(releaseTimer);
    }
    releaseTimer = setTimeout(() => {
        if (isRecordingActive) {
            console.log('⏰ Safety timeout - stopping recording after 30 seconds');
            handleShortcutRelease();
        }
    }, 30000); // Safety timeout after 30 seconds
}
function handleShortcutRelease() {
    if (!mainWindow || !isRecordingActive)
        return;
    console.log('🛑 Stopping recording and starting transcription...');
    isRecordingActive = false;
    // Clear the release timer
    if (releaseTimer) {
        clearTimeout(releaseTimer);
        releaseTimer = null;
    }
    // Notify widget to hide recording animation
    mainWindow.webContents.send('recording-stopped');
    // Stop recording and process transcription
    mainWindow.webContents.send('stop-recording-from-shortcut');
}
async function transcribeAudio(audioBuffer) {
    return new Promise((resolve, reject) => {
        try {
            if (!OPENAI_API_KEY) {
                throw new Error('OpenAI API key is not set in .env file');
            }
            console.log('Audio buffer size:', audioBuffer.length);
            if (audioBuffer.length === 0) {
                throw new Error('Empty audio buffer received');
            }
            // Create a temporary file to store the audio
            const tempFilePath = path.join(os.tmpdir(), `audio-${Date.now()}.wav`);
            console.log('Writing audio to temporary file:', tempFilePath);
            fs.writeFileSync(tempFilePath, audioBuffer);
            // Verify the file was written
            const fileStats = fs.statSync(tempFilePath);
            console.log('Temporary file size:', fileStats.size);
            if (fileStats.size === 0) {
                throw new Error('Failed to write audio data to temporary file');
            }
            // Create form data
            const form = new form_data_1.default();
            form.append('file', fs.createReadStream(tempFilePath), {
                filename: 'audio.wav',
                contentType: 'audio/wav'
            });
            form.append('model', 'whisper-1');
            form.append('response_format', 'text');
            console.log('Form data created, sending to OpenAI API...');
            // Set up the request options
            const options = {
                hostname: 'api.openai.com',
                path: '/v1/audio/transcriptions',
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${OPENAI_API_KEY}`,
                    ...form.getHeaders()
                }
            };
            console.log('Making request to OpenAI API...');
            // Make the request
            const req = https.request(options, (res) => {
                let data = '';
                res.on('data', (chunk) => {
                    data += chunk;
                });
                res.on('end', () => {
                    console.log('Response received, status code:', res.statusCode);
                    // Clean up the temporary file
                    try {
                        fs.unlinkSync(tempFilePath);
                        console.log('Temporary file deleted');
                    }
                    catch (err) {
                        console.error('Error deleting temporary file:', err);
                    }
                    if (res.statusCode !== 200) {
                        console.error('OpenAI API error:', res.statusCode, data);
                        reject(new Error(`OpenAI API error: ${res.statusCode} ${data}`));
                        return;
                    }
                    console.log('Transcription successful');
                    resolve(data);
                });
            });
            req.on('error', (error) => {
                console.error('Request error:', error);
                // Clean up the temporary file
                try {
                    fs.unlinkSync(tempFilePath);
                    console.log('Temporary file deleted');
                }
                catch (err) {
                    console.error('Error deleting temporary file:', err);
                }
                reject(error);
            });
            // Send the form data
            form.pipe(req);
            console.log('Request sent');
        }
        catch (error) {
            console.error('Error transcribing audio:', error);
            reject(error);
        }
    });
}
function isEnglishText(text) {
    // Check if text contains mostly English characters
    const englishPattern = /^[a-zA-Z0-9\s.,!?'"()-]+$/;
    const englishWords = ['the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'have', 'has', 'had', 'will', 'would', 'could', 'should', 'can', 'may', 'might', 'must', 'shall', 'do', 'does', 'did', 'get', 'got', 'go', 'went', 'come', 'came', 'see', 'saw', 'know', 'knew', 'think', 'thought', 'say', 'said', 'tell', 'told', 'give', 'gave', 'take', 'took', 'make', 'made', 'want', 'need', 'like', 'love', 'help', 'work', 'play', 'look', 'find', 'use', 'try', 'ask', 'feel', 'seem', 'become', 'leave', 'put', 'mean', 'keep', 'let', 'begin', 'start', 'show', 'hear', 'play', 'run', 'move', 'live', 'believe', 'hold', 'bring', 'happen', 'write', 'provide', 'sit', 'stand', 'lose', 'pay', 'meet', 'include', 'continue', 'set', 'learn', 'change', 'lead', 'understand', 'watch', 'follow', 'stop', 'create', 'speak', 'read', 'allow', 'add', 'spend', 'grow', 'open', 'walk', 'win', 'offer', 'remember', 'consider', 'appear', 'buy', 'wait', 'serve', 'die', 'send', 'expect', 'build', 'stay', 'fall', 'cut', 'reach', 'kill', 'remain'];
    // If text contains non-English characters, it's likely not English
    if (!englishPattern.test(text.replace(/[^\x00-\x7F]/g, ''))) {
        return false;
    }
    // Check if text contains common English words
    const words = text.toLowerCase().split(/\s+/);
    const englishWordCount = words.filter(word => englishWords.includes(word.replace(/[.,!?'"()-]/g, ''))).length;
    // If more than 20% of words are common English words, consider it English
    return englishWordCount / words.length > 0.2;
}
async function detectAndTranslate(text) {
    return new Promise((resolve, reject) => {
        try {
            // If text is already in English, return it as-is
            if (isEnglishText(text)) {
                console.log('Text detected as English, returning original text');
                resolve(text);
                return;
            }
            console.log('Text detected as non-English, translating...');
            if (!OPENAI_API_KEY) {
                throw new Error('OpenAI API key is not set in .env file');
            }
            const data = JSON.stringify({
                model: 'gpt-3.5-turbo',
                messages: [
                    {
                        role: 'system',
                        content: 'You are a literal translation assistant. Translate the given text to English word-for-word. Do not interpret, explain, answer questions, or add any extra content. Only provide the direct English translation of the exact words spoken. If someone says "I need leave tomorrow", translate it as "I need leave tomorrow" - do not change it to "I need to take leave tomorrow" or provide suggestions.'
                    },
                    {
                        role: 'user',
                        content: `Translate this text to English literally: "${text}"`
                    }
                ],
                temperature: 0.0
            });
            // Set up the request options
            const options = {
                hostname: 'api.openai.com',
                path: '/v1/chat/completions',
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${OPENAI_API_KEY}`,
                    'Content-Length': Buffer.byteLength(data)
                }
            };
            // Make the request
            const req = https.request(options, (res) => {
                let responseData = '';
                res.on('data', (chunk) => {
                    responseData += chunk;
                });
                res.on('end', () => {
                    if (res.statusCode !== 200) {
                        reject(new Error(`OpenAI API error: ${res.statusCode} ${responseData}`));
                        return;
                    }
                    try {
                        const parsedData = JSON.parse(responseData);
                        resolve(parsedData.choices[0].message.content.trim());
                    }
                    catch (error) {
                        reject(new Error(`Error parsing OpenAI response: ${error.message || 'Unknown error'}`));
                    }
                });
            });
            req.on('error', (error) => {
                reject(error);
            });
            // Send the data
            req.write(data);
            req.end();
        }
        catch (error) {
            console.error('Error detecting and translating:', error);
            reject(error);
        }
    });
}
// IPC Handlers
electron_1.ipcMain.on('hide-widget', () => {
    mainWindow?.hide();
});
electron_1.ipcMain.on('resize-window', (_event, width, height) => {
    if (mainWindow) {
        mainWindow.setSize(width, height);
    }
});
// New IPC handler for recording completion notification
electron_1.ipcMain.on('recording-completed', () => {
    // Reset recording state when transcription is completed
    isRecordingActive = false;
    console.log('Recording and transcription completed');
});
electron_1.ipcMain.handle('transcribe-audio', async (_event, audioData) => {
    try {
        console.log('🎤 Starting transcription process...');
        console.log('📊 Audio data received:');
        console.log('   - Size:', audioData.size, 'bytes');
        console.log('   - MIME type:', audioData.mimeType);
        console.log('   - Base64 length:', audioData.base64Audio.length);
        // Check if OpenAI API key is available
        if (!OPENAI_API_KEY) {
            throw new Error('OpenAI API key is not configured');
        }
        // Check if the data is valid
        if (!audioData.base64Audio || audioData.base64Audio.length === 0) {
            throw new Error('Received empty audio data');
        }
        // Convert base64 to buffer
        const buffer = Buffer.from(audioData.base64Audio, 'base64');
        console.log('✅ Audio buffer created, size:', buffer.length, 'bytes');
        // Use the actual OpenAI API for transcription
        console.log('Starting transcription with OpenAI API...');
        try {
            // Create a temporary file to store the audio
            const tempFilePath = path.join(os.tmpdir(), `audio-${Date.now()}.wav`);
            console.log('Writing audio to temporary file:', tempFilePath);
            fs.writeFileSync(tempFilePath, buffer);
            // Verify the file was written
            const fileStats = fs.statSync(tempFilePath);
            console.log('Temporary file size:', fileStats.size);
            // Create form data
            const form = new form_data_1.default();
            // Determine the correct filename and content type based on the audio format
            let filename = 'audio.webm';
            let contentType = audioData.mimeType;
            if (audioData.mimeType.includes('webm')) {
                filename = 'audio.webm';
                contentType = 'audio/webm';
            }
            else if (audioData.mimeType.includes('wav')) {
                filename = 'audio.wav';
                contentType = 'audio/wav';
            }
            form.append('file', fs.createReadStream(tempFilePath), {
                filename: filename,
                contentType: contentType
            });
            form.append('model', 'whisper-1');
            form.append('response_format', 'text');
            console.log('Sending request to OpenAI API...');
            // Make the request to OpenAI API
            return new Promise((resolve) => {
                const req = https.request({
                    hostname: 'api.openai.com',
                    path: '/v1/audio/transcriptions',
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${OPENAI_API_KEY}`,
                        ...form.getHeaders()
                    }
                }, async (res) => {
                    let data = '';
                    res.on('data', (chunk) => {
                        data += chunk;
                    });
                    res.on('end', async () => {
                        // Clean up the temporary file
                        try {
                            fs.unlinkSync(tempFilePath);
                            console.log('Temporary file deleted');
                        }
                        catch (err) {
                            console.error('Error deleting temporary file:', err);
                        }
                        if (res.statusCode !== 200) {
                            console.error('OpenAI API error:', res.statusCode, data);
                            resolve({
                                transcribedText: `Error: ${res.statusCode} - ${data}`,
                                additionalInfo: {
                                    error: true,
                                    statusCode: res.statusCode
                                }
                            });
                            return;
                        }
                        // Success - get the transcribed text
                        const transcription = data.trim();
                        console.log('🎯 Transcription successful:', transcription);
                        // Translate to English if needed
                        let finalText = transcription;
                        try {
                            finalText = await detectAndTranslate(transcription);
                            console.log('🌐 Translation completed:', finalText);
                        }
                        catch (err) {
                            console.error('⚠️ Translation failed, using original text:', err);
                        }
                        // Copy the translated text to clipboard
                        electron_1.clipboard.writeText(finalText);
                        console.log('📋 Text copied to clipboard:', finalText);
                        // Auto-paste the text after a short delay
                        setTimeout(() => {
                            console.log('Auto-pasting transcribed text...');
                            if (process.platform === 'win32') {
                                // Use PowerShell to send Ctrl+V
                                const pasteCommand = 'powershell -WindowStyle Hidden -Command "Add-Type -AssemblyName System.Windows.Forms; [System.Windows.Forms.SendKeys]::SendWait(\'^v\')"';
                                (0, child_process_1.exec)(pasteCommand, (error) => {
                                    if (error) {
                                        console.error('Auto-paste failed:', error.message);
                                    }
                                    else {
                                        console.log('Text auto-pasted successfully');
                                    }
                                });
                            }
                        }, 1000); // Wait 1 second to ensure clipboard is ready
                        // Return both the transcribed text and additional info
                        resolve({
                            transcribedText: finalText,
                            additionalInfo: {
                                audioSize: audioData.size,
                                mimeType: audioData.mimeType,
                                originalText: transcription !== finalText ? transcription : undefined
                            }
                        });
                    });
                });
                req.on('error', (error) => {
                    // Clean up the temporary file
                    try {
                        fs.unlinkSync(tempFilePath);
                        console.log('Temporary file deleted');
                    }
                    catch (err) {
                        console.error('Error deleting temporary file:', err);
                    }
                    console.error('Request error:', error);
                    resolve({
                        transcribedText: `Error: ${error.message}`,
                        additionalInfo: {
                            error: true
                        }
                    });
                });
                // Send the form data
                form.pipe(req);
                console.log('Request sent to OpenAI API');
            });
        }
        catch (error) {
            console.error('Error in transcription process:', error);
            return {
                transcribedText: `Error: ${error instanceof Error ? error.message : String(error)}`,
                additionalInfo: {
                    error: true
                }
            };
        }
    }
    catch (error) {
        console.error('Error handling transcribe-audio:', error);
        const errorMessage = "Error processing audio: " + (error instanceof Error ? error.message : String(error));
        return {
            transcribedText: errorMessage,
            additionalInfo: {
                error: true
            }
        };
    }
});
electron_1.ipcMain.handle('detect-and-translate', async (_event, text) => {
    try {
        const translatedText = await detectAndTranslate(text);
        return translatedText;
    }
    catch (error) {
        console.error('Error handling detect-and-translate:', error);
        throw error;
    }
});
// Settings IPC handlers
electron_1.ipcMain.handle('get-settings', async () => {
    return {
        globalShortcut: store.get('globalShortcut', 'Control+`')
    };
});
electron_1.ipcMain.handle('save-settings', async (_event, settings) => {
    try {
        const newShortcut = settings.globalShortcut;
        // Unregister current shortcut
        electron_1.globalShortcut.unregisterAll();
        // Register new shortcut
        if (newShortcut) {
            const success = electron_1.globalShortcut.register(newShortcut, () => {
                handleShortcutPress();
            });
            if (success) {
                // Save to store
                store.set('globalShortcut', newShortcut);
                currentShortcut = newShortcut;
                console.log('Shortcut updated to:', newShortcut);
                return { success: true };
            }
            else {
                // Re-register old shortcut if new one failed
                electron_1.globalShortcut.register(currentShortcut, () => {
                    handleShortcutPress();
                });
                throw new Error('Failed to register new shortcut. It may be in use by another application.');
            }
        }
        return { success: true };
    }
    catch (error) {
        console.error('Error saving settings:', error);
        throw error;
    }
});
electron_1.app.whenReady().then(() => {
    createWindow();
    createTray();
    // Unregister all shortcuts first to prevent conflicts
    electron_1.globalShortcut.unregisterAll();
    // Register press-and-hold shortcut
    const shortcutKey = 'Control+`';
    let keyPressCount = 0;
    let keyPressTimer = null;
    const success = electron_1.globalShortcut.register(shortcutKey, () => {
        keyPressCount++;
        if (keyPressCount === 1 && !isRecordingActive) {
            // First press - start recording
            console.log('🎤 Shortcut pressed - starting recording');
            handleShortcutPress();
            // Set up a timer to detect if key is being held
            keyPressTimer = setTimeout(() => {
                // Key is being held, continue recording
                console.log('🔄 Key being held, continuing recording...');
            }, 100);
        }
        else if (keyPressCount === 1 && isRecordingActive) {
            // Key released (no more presses) - stop recording
            console.log('🛑 Key released - stopping recording');
            handleShortcutRelease();
            keyPressCount = 0;
        }
        // Reset press count after a short delay to detect key release
        setTimeout(() => {
            if (keyPressCount === 1 && isRecordingActive) {
                // No more presses detected, key was released
                console.log('🛑 Key release detected - stopping recording');
                handleShortcutRelease();
            }
            keyPressCount = 0;
        }, 50);
    });
    // Register a simple toggle shortcut as backup
    const toggleShortcut = 'Control+Shift+`';
    electron_1.globalShortcut.register(toggleShortcut, () => {
        if (!isRecordingActive) {
            console.log('🎤 Toggle shortcut - starting recording');
            handleShortcutPress();
        }
        else {
            console.log('🛑 Toggle shortcut - stopping recording');
            handleShortcutRelease();
        }
    });
    if (success) {
        console.log('✅ Recording shortcuts registered:');
        console.log('   🎤 Hold: ' + shortcutKey + ' (press and hold)');
        console.log('   🔄 Toggle: ' + toggleShortcut + ' (press to start/stop)');
        currentShortcut = shortcutKey;
    }
    else {
        console.error('❌ Failed to register shortcuts');
    }
    electron_1.app.on('activate', () => {
        if (electron_1.BrowserWindow.getAllWindows().length === 0)
            createWindow();
    });
});
electron_1.app.on('window-all-closed', () => {
    if (process.platform !== 'darwin')
        electron_1.app.quit();
});
electron_1.app.on('will-quit', () => {
    electron_1.globalShortcut.unregisterAll();
});
// Add IPC handlers for dashboard
electron_1.ipcMain.handle('get-history', async () => {
    // Return transcription history from store
    return store.get('history', []);
});
electron_1.ipcMain.handle('login', async (_event, credentials) => {
    // Implement login logic here
    // This is a placeholder - you would typically call an API
    if (credentials.email && credentials.password) {
        const user = { name: credentials.email.split('@')[0], email: credentials.email };
        store.set('user', user);
        return { success: true, user };
    }
    return { success: false, message: 'Invalid credentials' };
});
electron_1.ipcMain.handle('logout', async () => {
    store.delete('user');
    return { success: true };
});
electron_1.ipcMain.handle('check-auth', async () => {
    const user = store.get('user');
    return { authenticated: !!user, user };
});
