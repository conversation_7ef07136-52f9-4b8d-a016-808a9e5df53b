"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const electron_1 = require("electron");
electron_1.contextBridge.exposeInMainWorld('electronAPI', {
    hideWidget: () => electron_1.ipcRenderer.send('hide-widget'),
    resizeWindow: (width, height) => electron_1.ipcRenderer.send('resize-window', width, height),
    transcribeAudio: (audioBlob) => electron_1.ipcRenderer.invoke('transcribe-audio', audioBlob),
    detectAndTranslate: (text) => electron_1.ipcRenderer.invoke('detect-and-translate', text),
    getSettings: () => electron_1.ipcRenderer.invoke('get-settings'),
    saveSettings: (settings) => electron_1.ipcRenderer.invoke('save-settings', settings),
    recordingCompleted: () => electron_1.ipcRenderer.send('recording-completed'),
    onTranscriptionComplete: (callback) => electron_1.ipcRenderer.on('transcription-complete', (_event, result) => callback(result)),
    onTranslationComplete: (callback) => electron_1.ipcRenderer.on('translation-complete', (_event, result) => callback(result)),
    onStartRecordingFromShortcut: (callback) => electron_1.ipcRenderer.on('start-recording-from-shortcut', callback),
    onStopRecordingFromShortcut: (callback) => electron_1.ipcRenderer.on('stop-recording-from-shortcut', callback),
});
