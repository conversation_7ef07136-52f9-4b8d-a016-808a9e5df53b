{"version": 3, "file": "log.js", "sourceRoot": "", "sources": ["../src/log.ts"], "names": [], "mappings": ";;;AACA,+BAA8B;AAC9B,iCAA0B;AAG1B,IAAI,OAAO,GAAuC,IAAI,CAAA;AAEzC,QAAA,KAAK,GAAG,eAAM,CAAC,kBAAkB,CAAC,CAAA;AAM/C,SAAgB,UAAU,CAAC,KAAyC;IAClE,OAAO,GAAG,KAAK,CAAA;AACjB,CAAC;AAFD,gCAEC;AAIY,QAAA,OAAO,GAAG,CAAC,CAAA;AAExB,MAAa,MAAM;IACjB,YAA+B,MAAsB;QAAtB,WAAM,GAAN,MAAM,CAAgB;QAErD,uBAAkB,GAAiD,EAAE,CAAC,EAAE,CAAC,EAAE,CAAA;IAFnB,CAAC;IAIzD,QAAQ,CAAC,IAAY;QACnB,MAAM,GAAG,GAAG,OAAO,CAAC,GAAG,EAAE,CAAA;QACzB,OAAO,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAA;IACrE,CAAC;IAED,mCAAmC;IACnC,IAAI,cAAc;QAChB,OAAO,aAAK,CAAC,OAAO,CAAA;IACtB,CAAC;IAED,IAAI,CAAC,eAAuC,EAAE,OAAgB;QAC5D,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,eAAe,EAAE,MAAM,CAAC,CAAA;IAC9C,CAAC;IAED,KAAK,CAAC,eAAuC,EAAE,OAAgB;QAC7D,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,eAAe,EAAE,OAAO,CAAC,CAAA;IAC/C,CAAC;IAED,IAAI,CAAC,eAAuC,EAAE,OAAgB;QAC5D,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,eAAe,EAAE,MAAM,CAAC,CAAA;IAC9C,CAAC;IAED,KAAK,CAAC,MAAqB,EAAE,OAAe;QAC1C,IAAI,aAAK,CAAC,OAAO,EAAE;YACjB,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,CAAA;SACtC;IACH,CAAC;IAEO,KAAK,CAAC,OAAmC,EAAE,eAAuC,EAAE,KAAe;QACzG,IAAI,OAAO,KAAK,SAAS,EAAE;YACzB,IAAI,CAAC,MAAM,CAAC,eAAyB,EAAE,IAAI,EAAE,KAAK,CAAC,CAAA;SACpD;aAAM;YACL,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,eAAgC,EAAE,KAAK,CAAC,CAAA;SAC9D;IACH,CAAC;IAEO,MAAM,CAAC,OAAuB,EAAE,MAAqB,EAAE,KAAe;QAC5E,yCAAyC;QACzC,IAAI,OAAO,YAAY,KAAK,EAAE;YAC5B,OAAO,GAAG,OAAO,CAAC,KAAK,IAAI,OAAO,CAAC,QAAQ,EAAE,CAAA;SAC9C;aAAM;YACL,OAAO,GAAG,OAAO,CAAC,QAAQ,EAAE,CAAA;SAC7B;QAED,MAAM,cAAc,GAAG,KAAK,KAAK,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAA;QACpD,MAAM,KAAK,GAAG,cAAc,CAAC,KAAK,CAAC,CAAA;QACnC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC,MAAM,CAAC,eAAO,CAAC,GAAG,KAAK,CAAC,cAAc,CAAC,GAAG,CAAC,CAAA;QACpE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,KAAK,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,eAAO,GAAG,CAAC,CAAC,+BAA+B,CAAC,CAAC,CAAA;QACnJ,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;IACzB,CAAC;IAED,MAAM,CAAC,aAAa,CAAC,OAAe,EAAE,MAAqB,EAAE,KAAe,EAAE,KAA6B,EAAE,cAAc,GAAG,CAAC;QAC7H,IAAI,MAAM,IAAI,IAAI,EAAE;YAClB,OAAO,OAAO,CAAA;SACf;QAED,MAAM,YAAY,GAAG,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAA;QACjE,IAAI,IAAI,GAAG,CAAC,KAAK,KAAK,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,YAAY,CAAA;QACxE,MAAM,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;QACtC,IAAI,OAAO,GAAG,CAAC,CAAA;QACf,KAAK,MAAM,IAAI,IAAI,UAAU,EAAE;YAC7B,IAAI,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,CAAA;YAC7B,IAAI,YAAY,GAAkB,IAAI,CAAA;YACtC,iCAAiC;YACjC,IAAI,UAAU,IAAI,IAAI,IAAI,OAAO,UAAU,KAAK,QAAQ,IAAI,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;gBACrF,YAAY,GAAG,GAAG,CAAC,MAAM,CAAC,cAAc,GAAG,OAAO,CAAC,MAAM,GAAG,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC,CAAA;gBACpF,UAAU,GAAG,UAAU,CAAC,OAAO,CAAC,UAAU,EAAE,KAAK,YAAY,EAAE,CAAC,CAAA;aACjE;iBAAM,IAAI,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE;gBACpC,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAA;aACxC;YAED,IAAI,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,UAAU,EAAE,CAAA;YACtC,IAAI,EAAE,OAAO,KAAK,UAAU,CAAC,MAAM,EAAE;gBACnC,IAAI,YAAY,IAAI,IAAI,EAAE;oBACxB,IAAI,IAAI,GAAG,CAAA;iBACZ;qBAAM;oBACL,IAAI,IAAI,IAAI,GAAG,YAAY,CAAA;iBAC5B;aACF;SACF;QACD,OAAO,IAAI,CAAA;IACb,CAAC;IAED,GAAG,CAAC,OAAe;QACjB,IAAI,OAAO,IAAI,IAAI,EAAE;YACnB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,OAAO,IAAI,CAAC,CAAA;SAClC;aAAM;YACL,OAAO,CAAC,OAAO,CAAC,CAAA;SACjB;IACH,CAAC;CACF;AA/FD,wBA+FC;AAED,MAAM,cAAc,GAA+B;IACjD,IAAI,EAAE,KAAK,CAAC,IAAI;IAChB,IAAI,EAAE,KAAK,CAAC,MAAM;IAClB,KAAK,EAAE,KAAK,CAAC,GAAG;IAChB,KAAK,EAAE,KAAK,CAAC,KAAK;CACnB,CAAA;AAEY,QAAA,GAAG,GAAG,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA", "sourcesContent": ["import { Chalk } from \"chalk\"\nimport * as chalk from \"chalk\"\nimport _debug from \"debug\"\nimport WritableStream = NodeJS.WritableStream\n\nlet printer: ((message: string) => void) | null = null\n\nexport const debug = _debug(\"electron-builder\")\n\nexport interface Fields {\n  [index: string]: any\n}\n\nexport function setPrinter(value: ((message: string) => void) | null) {\n  printer = value\n}\n\nexport type LogLevel = \"info\" | \"warn\" | \"debug\" | \"notice\" | \"error\"\n\nexport const PADDING = 2\n\nexport class Logger {\n  constructor(protected readonly stream: WritableStream) {}\n\n  messageTransformer: (message: string, level: LogLevel) => string = it => it\n\n  filePath(file: string) {\n    const cwd = process.cwd()\n    return file.startsWith(cwd) ? file.substring(cwd.length + 1) : file\n  }\n\n  // noinspection JSMethodCanBeStatic\n  get isDebugEnabled() {\n    return debug.enabled\n  }\n\n  info(messageOrFields: Fields | null | string, message?: string) {\n    this.doLog(message, messageOrFields, \"info\")\n  }\n\n  error(messageOrFields: Fields | null | string, message?: string) {\n    this.doLog(message, messageOrFields, \"error\")\n  }\n\n  warn(messageOrFields: Fields | null | string, message?: string): void {\n    this.doLog(message, messageOrFields, \"warn\")\n  }\n\n  debug(fields: Fields | null, message: string) {\n    if (debug.enabled) {\n      this._doLog(message, fields, \"debug\")\n    }\n  }\n\n  private doLog(message: string | undefined | Error, messageOrFields: Fields | null | string, level: LogLevel) {\n    if (message === undefined) {\n      this._doLog(messageOrFields as string, null, level)\n    } else {\n      this._doLog(message, messageOrFields as Fields | null, level)\n    }\n  }\n\n  private _doLog(message: string | Error, fields: Fields | null, level: LogLevel) {\n    // noinspection SuspiciousInstanceOfGuard\n    if (message instanceof Error) {\n      message = message.stack || message.toString()\n    } else {\n      message = message.toString()\n    }\n\n    const levelIndicator = level === \"error\" ? \"⨯\" : \"•\"\n    const color = LEVEL_TO_COLOR[level]\n    this.stream.write(`${\" \".repeat(PADDING)}${color(levelIndicator)} `)\n    this.stream.write(Logger.createMessage(this.messageTransformer(message, level), fields, level, color, PADDING + 2 /* level indicator and space */))\n    this.stream.write(\"\\n\")\n  }\n\n  static createMessage(message: string, fields: Fields | null, level: LogLevel, color: (it: string) => string, messagePadding = 0): string {\n    if (fields == null) {\n      return message\n    }\n\n    const fieldPadding = \" \".repeat(Math.max(2, 16 - message.length))\n    let text = (level === \"error\" ? color(message) : message) + fieldPadding\n    const fieldNames = Object.keys(fields)\n    let counter = 0\n    for (const name of fieldNames) {\n      let fieldValue = fields[name]\n      let valuePadding: string | null = null\n      // Remove unnecessary line breaks\n      if (fieldValue != null && typeof fieldValue === \"string\" && fieldValue.includes(\"\\n\")) {\n        valuePadding = \" \".repeat(messagePadding + message.length + fieldPadding.length + 2)\n        fieldValue = fieldValue.replace(/\\n\\s*\\n/g, `\\n${valuePadding}`)\n      } else if (Array.isArray(fieldValue)) {\n        fieldValue = JSON.stringify(fieldValue)\n      }\n\n      text += `${color(name)}=${fieldValue}`\n      if (++counter !== fieldNames.length) {\n        if (valuePadding == null) {\n          text += \" \"\n        } else {\n          text += \"\\n\" + valuePadding\n        }\n      }\n    }\n    return text\n  }\n\n  log(message: string): void {\n    if (printer == null) {\n      this.stream.write(`${message}\\n`)\n    } else {\n      printer(message)\n    }\n  }\n}\n\nconst LEVEL_TO_COLOR: { [index: string]: Chalk } = {\n  info: chalk.blue,\n  warn: chalk.yellow,\n  error: chalk.red,\n  debug: chalk.white,\n}\n\nexport const log = new Logger(process.stdout)\n"]}