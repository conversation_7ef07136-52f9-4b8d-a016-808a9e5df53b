{"version": 3, "file": "builder-cli.js", "sourceRoot": "", "sources": ["../../src/remoteBuilder/builder-cli.ts"], "names": [], "mappings": ";;AACA,uCAA8C;AAC9C,6BAA4B;AAC5B,+CAA8D;AAC9D,0CAAsC;AAItC,IAAI,OAAO,CAAC,GAAG,CAAC,kCAAkC,IAAI,IAAI,EAAE;IAC1D,OAAO,CAAC,GAAG,CAAC,kCAAkC,GAAG,MAAM,CAAA;CACxD;AAED,KAAK,UAAU,OAAO,CAAC,IAAe;IACpC,IAAI,OAAO,CAAC,GAAG,CAAC,mBAAmB,IAAI,IAAI,EAAE;QAC3C,MAAM,IAAI,wCAAyB,CAAC,yDAAyD,CAAC,CAAA;KAC/F;IAED,MAAM,UAAU,GAAG,OAAO,CAAC,GAAG,CAAC,WAAW,CAAA;IAC1C,IAAI,UAAU,IAAI,IAAI,EAAE;QACtB,MAAM,IAAI,wCAAyB,CAAC,iDAAiD,CAAC,CAAA;KACvF;IAED,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAA;IAC5B,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,EAAE;QACzB,MAAM,IAAI,wCAAyB,CAAC,wBAAwB,CAAC,CAAA;KAC9D;IACD,IAAI,OAAO,IAAI,IAAI,EAAE;QACnB,MAAM,IAAI,wCAAyB,CAAC,4BAA4B,CAAC,CAAA;KAClE;IACD,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;QAC3B,MAAM,IAAI,wCAAyB,CAAC,sCAAsC,CAAC,CAAA;KAC5E;IAED,MAAM,QAAQ,GAAG,UAAU,GAAG,IAAI,CAAC,GAAG,GAAG,WAAW,CAAA;IACpD,MAAM,IAAI,GAAG,MAAM,mBAAQ,CAAC,QAAQ,CAAC,CAAA;IAErC,MAAM,aAAa,GAAG,OAAO,CAAC,GAAG,CAAC,eAAe,CAAA;IACjD,IAAI,UAAU,IAAI,IAAI,EAAE;QACtB,MAAM,IAAI,wCAAyB,CAAC,qDAAqD,CAAC,CAAA;KAC3F;IAED,yCAAyC;IACzC,MAAM,WAAW,GAAG,UAAU,GAAG,IAAI,CAAC,GAAG,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,eAAe,CAAA;IACtE,uEAAuE;IACvE,MAAM,OAAO,GAAqC;QAChD,WAAW;QACX,UAAU;QACV,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,GAAG,GAAG,GAAG,EAAE,CAAC,IAAI,CAAC;QAC3D,OAAO,EAAE,OAAO;KACjB,CAAA;IACD,MAAM,QAAQ,GAAG,IAAI,mBAAQ,CAAC,OAAO,CAAC,CAAA;IAEtC,MAAM,SAAS,GAAwB,EAAE,CAAA;IACzC,MAAM,kBAAkB,GAAG,aAAc,CAAC,MAAM,GAAG,CAAC,CAAA;IACpD,QAAQ,CAAC,eAAe,CAAC,KAAK,CAAC,EAAE;QAC/B,IAAI,KAAK,CAAC,IAAI,IAAI,IAAI,EAAE;YACtB,OAAM;SACP;QAED,SAAS,CAAC,IAAI,CAAC;YACb,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC;YAC9C,MAAM,EAAE,KAAK,CAAC,MAAM,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI;YACvD,IAAI,EAAE,KAAK,CAAC,IAAI;YAChB,gBAAgB,EAAE,KAAK,CAAC,gBAAgB;YACxC,iBAAiB,EAAE,KAAK,CAAC,iBAAiB,KAAK,IAAI;YACnD,UAAU,EAAE,KAAK,CAAC,UAAU;SAC7B,CAAC,CAAA;IACJ,CAAC,CAAC,CAAA;IAEF,QAAQ,CAAC,sBAAsB,GAAG,CAAC,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,EAAE;QAC3D,uFAAuF;QACvF,MAAM,SAAS,GAAG,MAAM,CAAC,IAAI,KAAK,MAAM,IAAI,CAAE,MAAqB,CAAC,gBAAgB,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,UAAU,CAAA;QACjH,OAAO,GAAG,SAAS,GAAG,IAAI,CAAC,GAAG,KAAK,MAAM,CAAC,IAAI,IAAI,mBAAI,CAAC,IAAI,CAAC,EAAE,CAAA;IAChE,CAAC,CAAA;IAED,2FAA2F;IAC3F,MAAM,QAAQ,CAAC,MAAM,CACnB;QACE,GAAG,IAAI,CAAC,aAAa;QACrB,OAAO,EAAE,IAAI;QACb,WAAW,EAAE,IAAI;QACjB,SAAS,EAAE,IAAI;QACf,SAAS,EAAE,IAAI;QACf,qBAAqB,EAAE,IAAI;QAC3B,gBAAgB,EAAE,IAAI;QACtB,WAAW,EAAE;YACX,MAAM,EAAE,aAAa;YACrB,cAAc,EAAE,GAAG,UAAU,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,oBAAoB,EAAE;SACvE;KACF,EACD,IAAI,CAAC,QAAQ,EACb,IAAI,CAAC,WAAW,EAChB,IAAI,CAAC,cAAc,CACpB,CAAA;IAED,6EAA6E;IAC7E,MAAM,oBAAS,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,mBAAoB,EAAE,qBAAqB,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,CAAA;AAChH,CAAC;AAED,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;IACjD,OAAO,CAAC,QAAQ,GAAG,CAAC,CAAA;IACpB,OAAO,oBAAS,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,mBAAoB,EAAE,qBAAqB,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,IAAI,KAAK,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAA;AACzH,CAAC,CAAC,CAAA", "sourcesContent": ["import { PublishOptions, UploadTask } from \"electron-publish\"\nimport { readJson, writeFile } from \"fs-extra\"\nimport * as path from \"path\"\nimport { Arch, InvalidConfigurationError } from \"builder-util\"\nimport { Packager } from \"../packager\"\nimport { PackagerOptions } from \"../packager<PERSON><PERSON>\"\nimport SnapTarget from \"../targets/snap\"\n\nif (process.env.BUILDER_REMOVE_STAGE_EVEN_IF_DEBUG == null) {\n  process.env.BUILDER_REMOVE_STAGE_EVEN_IF_DEBUG = \"true\"\n}\n\nasync function doBuild(data: BuildTask): Promise<void> {\n  if (process.env.APP_BUILDER_TMP_DIR == null) {\n    throw new InvalidConfigurationError(\"Env APP_BUILDER_TMP_DIR must be set for builder process\")\n  }\n\n  const projectDir = process.env.PROJECT_DIR\n  if (projectDir == null) {\n    throw new InvalidConfigurationError(\"Env PROJECT_DIR must be set for builder process\")\n  }\n\n  const targets = data.targets\n  if (data.platform == null) {\n    throw new InvalidConfigurationError(\"platform not specified\")\n  }\n  if (targets == null) {\n    throw new InvalidConfigurationError(\"targets path not specified\")\n  }\n  if (!Array.isArray(targets)) {\n    throw new InvalidConfigurationError(\"targets must be array of target name\")\n  }\n\n  const infoFile = projectDir + path.sep + \"info.json\"\n  const info = await readJson(infoFile)\n\n  const projectOutDir = process.env.PROJECT_OUT_DIR\n  if (projectDir == null) {\n    throw new InvalidConfigurationError(\"Env PROJECT_OUT_DIR must be set for builder process\")\n  }\n\n  // yes, for now we expect the only target\n  const prepackaged = projectDir + path.sep + targets[0].unpackedDirName\n  // do not use build function because we don't need to publish artifacts\n  const options: PackagerOptions & PublishOptions = {\n    prepackaged,\n    projectDir,\n    [data.platform]: targets.map(it => it.name + \":\" + it.arch),\n    publish: \"never\",\n  }\n  const packager = new Packager(options)\n\n  const artifacts: Array<ArtifactInfo> = []\n  const relativePathOffset = projectOutDir!.length + 1\n  packager.artifactCreated(event => {\n    if (event.file == null) {\n      return\n    }\n\n    artifacts.push({\n      file: event.file.substring(relativePathOffset),\n      target: event.target == null ? null : event.target.name,\n      arch: event.arch,\n      safeArtifactName: event.safeArtifactName,\n      isWriteUpdateInfo: event.isWriteUpdateInfo === true,\n      updateInfo: event.updateInfo,\n    })\n  })\n\n  packager.stageDirPathCustomizer = (target, packager, arch) => {\n    // snap creates a lot of files and so, we cannot use tmpfs to avoid out of memory error\n    const parentDir = target.name === \"snap\" && !(target as SnapTarget).isUseTemplateApp ? projectOutDir : projectDir\n    return `${parentDir}${path.sep}__${target.name}-${Arch[arch]}`\n  }\n\n  // _build method expects final effective configuration - packager.options.config is ignored\n  await packager._build(\n    {\n      ...info.configuration,\n      publish: null,\n      beforeBuild: null,\n      afterPack: null,\n      afterSign: null,\n      afterAllArtifactBuild: null,\n      onNodeModuleFile: null,\n      directories: {\n        output: projectOutDir,\n        buildResources: `${projectDir}${path.sep}${info.buildResourceDirName}`,\n      },\n    },\n    info.metadata,\n    info.devMetadata,\n    info.repositoryInfo\n  )\n\n  // writeJson must be not used because it adds unwanted \\n as last file symbol\n  await writeFile(path.join(process.env.APP_BUILDER_TMP_DIR!, \"__build-result.json\"), JSON.stringify(artifacts))\n}\n\ndoBuild(JSON.parse(process.argv[2])).catch(error => {\n  process.exitCode = 0\n  return writeFile(path.join(process.env.APP_BUILDER_TMP_DIR!, \"__build-result.json\"), (error.stack || error).toString())\n})\n\ninterface TargetInfo {\n  name: string\n  arch: string\n  unpackedDirName: string\n}\n\ninterface ArtifactInfo extends UploadTask {\n  target: string | null\n\n  readonly isWriteUpdateInfo?: boolean\n  readonly updateInfo?: any\n}\n\ninterface BuildTask {\n  platform: string\n  targets: Array<TargetInfo>\n}\n"]}