const { ipcRenderer } = require('electron');

const widgetIcon = document.getElementById('widget-icon');
const tooltip = document.getElementById('tooltip');
const micIcon = document.querySelector('.mic-icon');
let isRecording = false;
let mediaRecorder = null;
let audioChunks = [];

// Show tooltip on hover
widgetIcon.addEventListener('mouseenter', () => {
  tooltip.style.display = 'block';
});

widgetIcon.addEventListener('mouseleave', () => {
  tooltip.style.display = 'none';
});

// Listen for recording start/stop events from main process
ipcRenderer.on('recording-started', () => {
  micIcon.style.backgroundColor = '#ff4444';
  micIcon.style.animation = 'pulse 1s infinite';
  tooltip.innerText = 'Recording... Release shortcut to stop';
  isRecording = true;
});

ipcRenderer.on('recording-stopped', () => {
  micIcon.style.backgroundColor = '#4CAF50';
  micIcon.style.animation = 'none';
  tooltip.innerText = 'Press shortcut to record';
  isRecording = false;
});

// Handle start recording from shortcut
ipcRenderer.on('start-recording-from-shortcut', async () => {
  try {
    const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
    audioChunks = [];

    mediaRecorder = new MediaRecorder(stream);

    mediaRecorder.ondataavailable = (event) => {
      if (event.data.size > 0) {
        audioChunks.push(event.data);
      }
    };

    mediaRecorder.onstop = async () => {
      const audioBlob = new Blob(audioChunks, { type: 'audio/wav' });
      const arrayBuffer = await audioBlob.arrayBuffer();
      const base64Audio = btoa(String.fromCharCode(...new Uint8Array(arrayBuffer)));

      // Send audio data to main process for transcription
      const result = await ipcRenderer.invoke('transcribe-audio', {
        base64Audio: base64Audio,
        mimeType: audioBlob.type,
        size: audioBlob.size
      });

      console.log('Transcription result:', result);

      // Hide widget after transcription
      setTimeout(() => {
        ipcRenderer.send('hide-widget');
      }, 1000);

      // Notify main process that recording is completed
      ipcRenderer.send('recording-completed');

      // Stop all tracks
      stream.getTracks().forEach(track => track.stop());
    };

    mediaRecorder.start();
    console.log('Recording started');
  } catch (error) {
    console.error('Error starting recording:', error);
    tooltip.innerText = 'Microphone access denied';
  }
});

// Handle stop recording from shortcut
ipcRenderer.on('stop-recording-from-shortcut', () => {
  if (mediaRecorder && mediaRecorder.state === 'recording') {
    mediaRecorder.stop();
    console.log('Recording stopped');
  }
});

// Add CSS for pulse animation
const style = document.createElement('style');
style.textContent = `
  @keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
  }
`;
document.head.appendChild(style);
