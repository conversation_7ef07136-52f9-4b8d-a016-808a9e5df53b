const { ipc<PERSON><PERSON><PERSON> } = require('electron');

const widgetIcon = document.getElementById('widget-icon');
const tooltip = document.getElementById('tooltip');
let isRecording = false;

// Show tooltip on hover
widgetIcon.addEventListener('mouseenter', () => {
  tooltip.style.display = 'block';
});

widgetIcon.addEventListener('mouseleave', () => {
  tooltip.style.display = 'none';
});

// Start/stop recording on click
widgetIcon.addEventListener('click', () => {
  if (!isRecording) {
    ipcRenderer.send('start-recording-from-widget');
    tooltip.innerText = 'Recording... Click to stop';
    isRecording = true;
  } else {
    ipcRenderer.send('stop-recording-from-widget');
    tooltip.innerText = 'Start Recording';
    isRecording = false;
  }
});
