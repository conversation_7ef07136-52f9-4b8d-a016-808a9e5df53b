{"name": "node-key-sender", "version": "1.0.11", "description": "NodeJs lib to send keyboard input to the operational system.", "main": "key-sender.js", "scripts": {"setup": "npm install"}, "repository": "https://github.com/garimpeiro-it/node-key-sender", "author": "<PERSON><PERSON><PERSON><PERSON>", "keywords": ["nodejs", "key", "keyboard", "input", "send", "keystroke", "os", "operational", "system", "java", "jar"], "bugs": {"email": "<EMAIL>", "url": "https://github.com/garimpeiro-it/node-key-sender/issues"}, "license": "MIT", "devDependencies": {}, "dependencies": {}}