{"name": "electron-osx-sign", "version": "0.6.0", "description": "Codesign Electron macOS apps", "main": "index.js", "bin": {"electron-osx-flat": "bin/electron-osx-flat.js", "electron-osx-sign": "bin/electron-osx-sign.js"}, "repository": {"type": "git", "url": "git+https://github.com/electron-userland/electron-osx-sign.git"}, "author": "<PERSON><PERSON><PERSON>", "license": "BSD-2-<PERSON><PERSON>", "bugs": {"url": "https://github.com/electron-userland/electron-osx-sign/issues"}, "homepage": "https://github.com/electron-userland/electron-osx-sign", "dependencies": {"bluebird": "^3.5.0", "compare-version": "^0.1.2", "debug": "^2.6.8", "isbinaryfile": "^3.0.2", "minimist": "^1.2.0", "plist": "^3.0.1"}, "devDependencies": {"electron-download": "^4.1.0", "eslint": "^4.2.0", "eslint-config-eslint": "^4.0.0", "extract-zip": "^1.6.5", "mkdirp": "^0.5.1", "rimraf": "^2.6.1", "run-series": "^1.1.4", "run-waterfall": "^1.1.3", "standard": "^10.0.2", "tape": "^4.7.0"}, "scripts": {"code-standard": "standard", "pretest": "rimraf test/work", "test": "standard && tape test"}, "standard": {"ignore": ["test/work"]}, "engines": {"node": ">=4.0.0"}}