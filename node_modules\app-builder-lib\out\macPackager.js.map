{"version": 3, "file": "macPackager.js", "sourceRoot": "", "sources": ["../src/macPackager.ts"], "names": [], "mappings": ";;AAAA,+CAA0C;AAC1C,+CAA2H;AAC3H,yDAA0D;AAC1D,0CAA4C;AAC5C,uCAA+B;AAC/B,6BAA4B;AAC5B,4CAA0E;AAC1E,sDAA2D;AAC3D,uCAAmC;AACnC,wDAAsJ;AACtJ,iCAAqD;AAIrD,yDAAoE;AACpE,2DAAuD;AACvD,uCAAkE;AAClE,2DAAwE;AACxE,sDAAuD;AACvD,oDAAoD;AACpD,kCAAiC;AAEjC,MAAqB,WAAY,SAAQ,mCAAkC;IAyBzE,YAAY,IAAc;QACxB,KAAK,CAAC,IAAI,EAAE,eAAQ,CAAC,GAAG,CAAC,CAAA;QAzBlB,oBAAe,GAAG,IAAI,eAAI,CAAkB,GAAG,EAAE;YACxD,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,EAAE,CAAA;YACjC,IAAI,OAAO,IAAI,IAAI,IAAI,OAAO,CAAC,QAAQ,KAAK,QAAQ,EAAE;gBACpD,OAAO,OAAO,CAAC,OAAO,CAAC,EAAE,YAAY,EAAE,OAAO,CAAC,GAAG,CAAC,YAAY,IAAI,IAAI,EAAE,CAAC,CAAA;aAC3E;YAED,OAAO,4BAAc,CAAC;gBACpB,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,cAAc;gBAChC,OAAO;gBACP,cAAc,EAAE,IAAI,CAAC,cAAc,EAAE;gBACrC,QAAQ,EAAE,gCAAa,CAAC,IAAI,CAAC,4BAA4B,CAAC,gBAAgB,EAAE,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC;gBAC3G,eAAe,EAAE,gCAAa,CAAC,IAAI,CAAC,4BAA4B,CAAC,uBAAuB,EAAE,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC;gBACjI,UAAU,EAAE,IAAI,CAAC,UAAU;aAC5B,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;gBACf,MAAM,YAAY,GAAG,MAAM,CAAC,YAAY,CAAA;gBACxC,IAAI,YAAY,IAAI,IAAI,EAAE;oBACxB,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,GAAG,EAAE,CAAC,4BAAc,CAAC,YAAY,CAAC,CAAC,CAAA;iBACnE;gBACD,OAAO,MAAM,CAAA;YACf,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;QAEM,cAAS,GAAG,IAAI,eAAI,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAA;IAIjE,CAAC;IAED,IAAI,aAAa;QACf,OAAO,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,mBAAmB,CAAA;IAChD,CAAC;IAED,6DAA6D;IACnD,cAAc,CAAC,OAAgB;QACvC,OAAO,IAAI,iBAAO,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,4BAA4B,CAAC,aAAa,EAAE,IAAI,CAAC,4BAA4B,CAAC,CAAA;IACnH,CAAC;IAED,KAAK,CAAC,WAAW;QACf,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,CAAA;IAC7B,CAAC;IAED,aAAa,CAAC,OAAsB,EAAE,MAAmE;QACvG,KAAK,MAAM,IAAI,IAAI,OAAO,EAAE;YAC1B,QAAQ,IAAI,EAAE;gBACZ,KAAK,iBAAU;oBACb,MAAK;gBAEP,KAAK,KAAK,CAAC,CAAC;oBACV,8DAA8D;oBAC9D,MAAM,EAAE,SAAS,EAAE,GAAG,OAAO,CAAC,aAAa,CAAC,CAAA;oBAC5C,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE,CAAC,IAAI,SAAS,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,CAAA;oBACnD,MAAK;iBACN;gBAED,KAAK,KAAK;oBACR,oEAAoE;oBACpE,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE,CAAC,IAAI,6BAAa,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAA;oBACnE,MAAK;gBAEP,KAAK,KAAK;oBACR,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE,CAAC,IAAI,eAAS,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,CAAA;oBACnD,MAAK;gBAEP;oBACE,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE,CAAC,CAAC,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,0BAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,kCAAkB,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC,CAAA;oBAC9H,MAAK;aACR;SACF;IACH,CAAC;IAES,KAAK,CAAC,MAAM,CACpB,MAAc,EACd,SAAiB,EACjB,YAAkC,EAClC,IAAU,EACV,4BAA8C,EAC9C,OAAsB;;QAEtB,QAAQ,IAAI,EAAE;YACZ,OAAO,CAAC,CAAC;gBACP,OAAO,KAAK,CAAC,MAAM,CAAC,MAAM,EAAE,SAAS,EAAE,YAAY,EAAE,IAAI,EAAE,4BAA4B,EAAE,OAAO,CAAC,CAAA;aAClG;YACD,KAAK,mBAAI,CAAC,SAAS,CAAC,CAAC;gBACnB,MAAM,OAAO,GAAG,mBAAI,CAAC,GAAG,CAAA;gBACxB,MAAM,YAAY,GAAG,SAAS,GAAG,IAAI,GAAG,mBAAI,CAAC,OAAO,CAAC,CAAA;gBACrD,MAAM,KAAK,CAAC,MAAM,CAAC,MAAM,EAAE,YAAY,EAAE,YAAY,EAAE,OAAO,EAAE,4BAA4B,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,CAAA;gBACnH,MAAM,SAAS,GAAG,mBAAI,CAAC,KAAK,CAAA;gBAC5B,MAAM,eAAe,GAAG,SAAS,GAAG,IAAI,GAAG,mBAAI,CAAC,SAAS,CAAC,CAAA;gBAC1D,MAAM,KAAK,CAAC,MAAM,CAAC,MAAM,EAAE,eAAe,EAAE,YAAY,EAAE,SAAS,EAAE,4BAA4B,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,CAAA;gBACxH,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,CAAA;gBACrC,kBAAG,CAAC,IAAI,CACN;oBACE,QAAQ,EAAE,YAAY;oBACtB,IAAI,EAAE,mBAAI,CAAC,IAAI,CAAC;oBAChB,CAAC,GAAG,SAAS,CAAC,IAAI,EAAE,CAAC,EAAE,SAAS,CAAC,OAAO;oBACxC,SAAS,EAAE,kBAAG,CAAC,QAAQ,CAAC,SAAS,CAAC;iBACnC,EACD,WAAW,CACZ,CAAA;gBACD,MAAM,OAAO,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,MAAM,CAAA;gBACrD,MAAM,EAAE,gBAAgB,EAAE,GAAG,OAAO,CAAC,qBAAqB,CAAC,CAAA;gBAC3D,MAAM,gBAAgB,CAAC;oBACrB,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,OAAO,CAAC;oBAC5C,YAAY,EAAE,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,OAAO,CAAC;oBACjD,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC;oBACzC,KAAK,EAAE,IAAI;oBACX,UAAU,EAAE,MAAA,4BAA4B,CAAC,UAAU,mCAAI,IAAI;oBAC3D,eAAe,EAAE,4BAA4B,CAAC,eAAe;oBAC7D,YAAY,EAAE,4BAA4B,CAAC,YAAY;iBACxD,CAAC,CAAA;gBACF,MAAM,EAAE,CAAC,EAAE,CAAC,YAAY,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAA;gBAC3D,MAAM,EAAE,CAAC,EAAE,CAAC,eAAe,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAA;gBAE9D,oGAAoG;gBACpG,MAAM,WAAW,GAAqB;oBACpC,SAAS;oBACT,MAAM;oBACN,IAAI;oBACJ,OAAO;oBACP,QAAQ,EAAE,IAAI;oBACd,oBAAoB,EAAE,YAAY;iBACnC,CAAA;gBACD,MAAM,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAA;gBAEtC,MAAM,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,SAAS,EAAE,YAAY,EAAE,IAAI,EAAE,4BAA4B,EAAE,OAAO,CAAC,CAAA;gBACxG,MAAK;aACN;SACF;IACH,CAAC;IAED,KAAK,CAAC,IAAI,CAAC,MAAc,EAAE,IAAU,EAAE,OAAsB,EAAE,WAA6B;QAC1F,IAAI,aAAa,GAAwB,IAAI,CAAA;QAE7C,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,KAAK,CAAC,IAAI,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC,IAAI,KAAK,SAAS,CAAC,CAAA;QACrG,MAAM,WAAW,GAAG,IAAI,CAAC,eAAe,CAAC,WAAW,CAAA;QAEpD,IAAI,CAAC,MAAM,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;YACjC,MAAM,OAAO,GAAG,WAAW,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,CAAA;YACzI,aAAa,GAAG,CACd,WAAW;gBACT,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE;gBACnB,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,QAAgC,EAAE,IAAI,EAAE,IAAI,CAAC,4BAA4B,EAAE,OAAO,CAAC,CACjJ,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,4BAA4B,CAAC,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,WAAW,CAAC,CAAC,CAAA;SACrF;QAED,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE;YAC5B,MAAM,UAAU,GAAG,MAAM,CAAC,IAAI,CAAA;YAC9B,IAAI,CAAC,CAAC,UAAU,KAAK,KAAK,IAAI,UAAU,KAAK,SAAS,CAAC,EAAE;gBACvD,SAAQ;aACT;YAED,MAAM,eAAe,GAAG,yBAAU,CAAC,EAAE,EAAE,IAAI,CAAC,4BAA4B,EAAE,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAA;YAC1F,IAAI,UAAU,KAAK,SAAS,EAAE;gBAC5B,yBAAU,CAAC,eAAe,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;oBAC9C,IAAI,EAAE,aAAa;iBACpB,CAAC,CAAA;aACH;YAED,MAAM,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,UAAU,GAAG,4BAAa,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;YAC7E,IAAI,WAAW,IAAI,IAAI,EAAE;gBACvB,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,YAAY,EAAE,KAAK,EAAE,IAAI,EAAE,eAAe,EAAE,CAAC,MAAM,CAAC,CAAC,CAAA;gBAC/E,MAAM,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,MAAM,CAAC,EAAE,YAAY,EAAE,eAAe,EAAE,IAAI,CAAC,CAAA;aACrH;iBAAM;gBACL,MAAM,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,YAAY,EAAE,eAAe,EAAE,IAAI,CAAC,CAAA;aAClE;SACF;QAED,IAAI,aAAa,IAAI,IAAI,EAAE;YACzB,MAAM,aAAa,CAAA;SACpB;IACH,CAAC;IAEO,KAAK,CAAC,IAAI,CAAC,OAAe,EAAE,MAAqB,EAAE,UAAmC,EAAE,IAAiB;QAC/G,IAAI,CAAC,2BAAa,EAAE,EAAE;YACpB,OAAM;SACP;QAED,MAAM,KAAK,GAAG,UAAU,IAAI,IAAI,CAAA;QAChC,MAAM,OAAO,GAAG,UAAU,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC,CAAC,UAAU,CAAA;QACnF,MAAM,SAAS,GAAG,OAAO,CAAC,QAAQ,CAAA;QAElC,IAAI,CAAC,KAAK,IAAI,SAAS,KAAK,IAAI,EAAE;YAChC,IAAI,IAAI,CAAC,gBAAgB,EAAE;gBACzB,MAAM,IAAI,wCAAyB,CAAC,yEAAyE,CAAC,CAAA;aAC/G;YACD,kBAAG,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,oCAAoC,EAAE,EAAE,4BAA4B,CAAC,CAAA;YACxF,OAAM;SACP;QAED,MAAM,YAAY,GAAG,CAAC,MAAM,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,YAAY,CAAA;QACpE,MAAM,YAAY,GAAG,OAAO,CAAC,IAAI,CAAA;QACjC,MAAM,IAAI,GAAG,YAAY,IAAI,cAAc,CAAA;QAC3C,MAAM,aAAa,GAAG,IAAI,KAAK,aAAa,CAAA;QAC5C,MAAM,gBAAgB,GAAG,mBAAmB,CAAC,KAAK,EAAE,aAAa,CAAC,CAAA;QAElE,IAAI,QAAQ,GAAG,IAAI,CAAA;QACnB,KAAK,MAAM,eAAe,IAAI,gBAAgB,EAAE;YAC9C,QAAQ,GAAG,MAAM,0BAAY,CAAC,eAAe,EAAE,SAAS,EAAE,YAAY,CAAC,CAAA;YACvE,IAAI,QAAQ,IAAI,IAAI,EAAE;gBACpB,MAAK;aACN;SACF;QAED,IAAI,QAAQ,IAAI,IAAI,EAAE;YACpB,IAAI,CAAC,KAAK,IAAI,CAAC,aAAa,IAAI,YAAY,KAAK,cAAc,EAAE;gBAC/D,QAAQ,GAAG,MAAM,0BAAY,CAAC,eAAe,EAAE,SAAS,EAAE,YAAY,CAAC,CAAA;gBACvE,IAAI,QAAQ,IAAI,IAAI,EAAE;oBACpB,kBAAG,CAAC,IAAI,CAAC,gGAAgG,CAAC,CAAA;iBAC3G;aACF;YAED,IAAI,QAAQ,IAAI,IAAI,EAAE;gBACpB,MAAM,yBAAW,CAAC,KAAK,EAAE,gBAAgB,EAAE,SAAS,EAAE,YAAY,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAA;gBAC1F,OAAM;aACP;SACF;QAED,IAAI,CAAC,gCAAiB,EAAE,EAAE;YACxB,MAAM,IAAI,wCAAyB,CAAC,+CAA+C,CAAC,CAAA;SACrF;QAED,IAAI,MAAM,GAAG,OAAO,CAAC,UAAU,CAAA;QAC/B,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;YACzB,IAAI,MAAM,CAAC,MAAM,IAAI,CAAC,EAAE;gBACtB,MAAM,GAAG,IAAI,CAAA;aACd;SACF;aAAM,IAAI,MAAM,IAAI,IAAI,EAAE;YACzB,MAAM,GAAG,MAAM,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAA;SAC/C;QAED,MAAM,QAAQ,GAAG,MAAM,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,MAAM,CAAC,EAAE,CAAC,CAAC,CAAA;QAEzE,IAAI,QAAQ,GAAG,OAAO,CAAC,QAAQ,IAAI,SAAS,CAAA;QAC5C,IAAI,QAAQ,EAAE;YACZ,kHAAkH;YAClH,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAC1B,QAAQ,CAAC,GAAG,CAAC,KAAK,EAAC,WAAW,EAAC,EAAE;gBAC/B,IAAI,MAAM,eAAU,CAAC,WAAW,CAAC,EAAE;oBACjC,OAAO,WAAW,CAAA;iBACnB;gBACD,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,WAAW,CAAC,CAAA;YAC3C,CAAC,CAAC,CACH,CAAA;YACD,kBAAG,CAAC,IAAI,CAAC,2CAA2C,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAA;SAC1F;QAED,MAAM,WAAW,GAAQ;YACvB,qBAAqB,EAAE,KAAK;YAC5B,oEAAoE;YACpE,+IAA+I;YAC/I,MAAM,EAAE,CAAC,IAAY,EAAE,EAAE;gBACvB,IAAI,QAAQ,IAAI,IAAI,EAAE;oBACpB,KAAK,MAAM,MAAM,IAAI,QAAQ,EAAE;wBAC7B,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;4BACrB,OAAO,IAAI,CAAA;yBACZ;qBACF;iBACF;gBACD,OAAO,CACL,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC;oBACtB,IAAI,CAAC,UAAU,CAAC,mBAAmB,EAAE,OAAO,CAAC,MAAM,CAAC;oBACpD,IAAI,CAAC,QAAQ,CAAC,yCAAyC,CAAC;oBACxD,IAAI,CAAC,QAAQ,CAAC,kDAAkD,CAAC;oBACjE,IAAI,CAAC,QAAQ,CAAC,0CAA0C,CAAC,CAC1D,CAAA;gBAED;;;oBAGI;YACN,CAAC;YACD,QAAQ,EAAE,QAAQ;YAClB,IAAI;YACJ,QAAQ,EAAE,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,QAAQ;YAClC,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,eAAe;YACpC,GAAG,EAAE,OAAO;YACZ,QAAQ,EAAE,YAAY,IAAI,SAAS;YACnC,QAAQ;YACR,SAAS,EAAE,KAAK,CAAC,CAAC,CAAC,UAAU,aAAV,UAAU,uBAAV,UAAU,CAAE,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,SAAS;YAC5D,YAAY,EAAE,KAAK,IAAI,IAAI,CAAC,4BAA4B,CAAC,YAAY,IAAI,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,4BAA4B,CAAC,YAAY,CAAC;YAClK,oEAAoE;YACpE,+EAA+E;YAC/E,mBAAmB,EAAE,OAAO,CAAC,gBAAgB,KAAK,IAAI;YACtD,oEAAoE;YACpE,eAAe,EAAE,OAAO,CAAC,YAAY;YACrC,eAAe,EAAE,KAAK,CAAC,CAAC,CAAC,UAAU,IAAI,UAAU,CAAC,eAAe,KAAK,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,eAAe,KAAK,KAAK;SAC/G,CAAA;QAED,MAAM,IAAI,CAAC,iBAAiB,CAAC,WAAW,EAAE,UAAU,CAAC,CAAA;QACrD,kBAAG,CAAC,IAAI,CACN;YACE,IAAI,EAAE,kBAAG,CAAC,QAAQ,CAAC,OAAO,CAAC;YAC3B,YAAY,EAAE,QAAQ,CAAC,IAAI;YAC3B,YAAY,EAAE,QAAQ,CAAC,IAAI;YAC3B,mBAAmB,EAAE,WAAW,CAAC,sBAAsB,CAAC,IAAI,MAAM;SACnE,EACD,SAAS,CACV,CAAA;QACD,MAAM,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,CAAA;QAE9B,2FAA2F;QAC3F,IAAI,UAAU,IAAI,IAAI,IAAI,CAAC,aAAa,EAAE;YACxC,MAAM,QAAQ,GAAG,aAAa,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,mCAAmC,CAAA;YACtF,MAAM,oBAAoB,GAAG,MAAM,0BAAY,CAAC,QAAQ,EAAE,UAAU,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAA;YAC5F,IAAI,oBAAoB,IAAI,IAAI,EAAE;gBAChC,MAAM,IAAI,wCAAyB,CAAC,sBAAsB,QAAQ,kFAAkF,CAAC,CAAA;aACtJ;YAED,kEAAkE;YAClE,MAAM,YAAY,GAAG,IAAI,CAAC,yBAAyB,CAAC,UAAU,EAAE,KAAK,EAAE,IAAI,CAAC,CAAA;YAC5E,MAAM,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,MAAO,EAAE,YAAY,CAAC,CAAA;YACrD,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,YAAY,EAAE,oBAAoB,EAAE,YAAY,CAAC,CAAA;YAC5E,MAAM,IAAI,CAAC,uBAAuB,CAAC,YAAY,EAAE,IAAI,EAAE,mBAAI,CAAC,GAAG,EAAE,IAAI,CAAC,uBAAuB,CAAC,YAAY,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,4BAA4B,CAAC,WAAW,CAAC,CAAC,CAAA;SAC/K;IACH,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAAC,WAAgB,EAAE,UAAmC;QACnF,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,YAAY,CAAA;QAC5C,MAAM,iBAAiB,GAAG,UAAU,IAAI,IAAI,CAAC,4BAA4B,CAAA;QACzE,MAAM,kBAAkB,GAAG,UAAU,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAA;QAE7D,IAAI,YAAY,GAAG,iBAAiB,CAAC,YAAY,CAAA;QACjD,IAAI,YAAY,IAAI,IAAI,EAAE;YACxB,MAAM,CAAC,GAAG,gBAAgB,kBAAkB,QAAQ,CAAA;YACpD,IAAI,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE;gBAC5B,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC,CAAC,CAAA;aACzD;iBAAM;gBACL,YAAY,GAAG,6BAAe,CAAC,wBAAwB,CAAC,CAAA;aACzD;SACF;QACD,WAAW,CAAC,YAAY,GAAG,YAAY,CAAA;QAEvC,IAAI,mBAAmB,GAAG,iBAAiB,CAAC,mBAAmB,CAAA;QAC/D,IAAI,mBAAmB,IAAI,IAAI,EAAE;YAC/B,MAAM,CAAC,GAAG,gBAAgB,kBAAkB,gBAAgB,CAAA;YAC5D,IAAI,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE;gBAC5B,mBAAmB,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC,CAAC,CAAA;aAChE;iBAAM;gBACL,mBAAmB,GAAG,6BAAe,CAAC,wBAAwB,CAAC,CAAA;aAChE;SACF;QACD,WAAW,CAAC,sBAAsB,CAAC,GAAG,mBAAmB,CAAA;QAEzD,IAAI,iBAAiB,CAAC,mBAAmB,IAAI,IAAI,EAAE;YACjD,WAAW,CAAC,sBAAsB,CAAC,GAAG,iBAAiB,CAAC,mBAAmB,CAAA;SAC5E;QACD,WAAW,CAAC,0BAA0B,CAAC,GAAG,iBAAiB,CAAC,uBAAuB,CAAA;IACrF,CAAC;IAED,kCAAkC;IACxB,KAAK,CAAC,MAAM,CAAC,IAAiB;QACtC,OAAO,6BAAS,CAAC,IAAI,CAAC,CAAA;IACxB,CAAC;IAED,kCAAkC;IACxB,KAAK,CAAC,MAAM,CAAC,OAAe,EAAE,OAAe,EAAE,QAAkB,EAAE,QAAmC;QAC9G,sDAAsD;QACtD,MAAM,gBAAK,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAA;QAEvD,MAAM,IAAI,GAAG,6BAAuB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAA;QACxD,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,OAAO,EAAE,eAAe,CAAC,CAAA;QAClD,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;QAClB,OAAO,MAAM,mBAAI,CAAC,cAAc,EAAE,IAAI,CAAC,CAAA;IACzC,CAAC;IAEM,iBAAiB,CAAC,IAAY;QACnC,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAA;IAClF,CAAC;IAEM,yBAAyB,CAAC,SAAiB;QAChD,OAAO,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAA;IACnE,CAAC;IAED,wBAAwB;IACxB,KAAK,CAAC,eAAe,CAAC,QAAa,EAAE,YAAoB;QACvD,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAA;QAC5B,MAAM,WAAW,GAAG,OAAO,CAAC,eAAe,CAAA;QAE3C,oEAAoE;QACpE,QAAQ,CAAC,kBAAkB,GAAG,WAAW,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,EAAE,WAAW,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,CAAA;QAE7I,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,WAAW,EAAE,CAAA;QACrC,IAAI,IAAI,IAAI,IAAI,EAAE;YAChB,MAAM,OAAO,GAAG,QAAQ,CAAC,gBAAgB,CAAA;YACzC,MAAM,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,WAAW,CAAC,CAAA;YAC1D,IAAI,OAAO,IAAI,IAAI,EAAE;gBACnB,MAAM,mBAAc,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC,CAAA;aACxD;YACD,MAAM,YAAY,GAAG,WAAW,CAAA;YAChC,QAAQ,CAAC,gBAAgB,GAAG,YAAY,CAAA;YACxC,MAAM,aAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,YAAY,CAAC,CAAC,CAAA;SAC7D;QACD,QAAQ,CAAC,YAAY,GAAG,OAAO,CAAC,WAAW,CAAA;QAC3C,QAAQ,CAAC,mBAAmB,GAAG,OAAO,CAAC,WAAW,CAAA;QAElD,MAAM,oBAAoB,GAAG,IAAI,CAAC,4BAA4B,CAAC,oBAAoB,CAAA;QACnF,IAAI,oBAAoB,IAAI,IAAI,EAAE;YAChC,QAAQ,CAAC,sBAAsB,GAAG,oBAAoB,CAAA;SACvD;QAED,QAAQ,CAAC,kBAAkB,GAAG,OAAO,CAAC,mBAAmB,CAAA;QAEzD,QAAQ,CAAC,0BAA0B,GAAG,IAAI,CAAC,4BAA4B,CAAC,kBAAkB,IAAI,OAAO,CAAC,OAAO,CAAA;QAC7G,QAAQ,CAAC,eAAe,GAAG,OAAO,CAAC,YAAY,CAAA;QAE/C,kBAAG,CAAC,IAAI,CAAC,4BAA4B,CAAC,QAAQ,IAAK,IAAI,CAAC,MAAc,CAAC,QAAQ,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,yBAAyB,GAAG,EAAE,CAAC,CAAC,CAAA;QACjI,QAAQ,CAAC,wBAAwB,GAAG,OAAO,CAAC,SAAS,CAAA;QAErD,IAAI,IAAI,CAAC,4BAA4B,CAAC,eAAe,EAAE;YACrD,QAAQ,CAAC,8BAA8B,GAAG,KAAK,CAAA;SAChD;QAED,MAAM,UAAU,GAAG,IAAI,CAAC,4BAA4B,CAAC,UAAU,CAAA;QAC/D,IAAI,UAAU,IAAI,IAAI,EAAE;YACtB,MAAM,CAAC,MAAM,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAA;SACpC;IACH,CAAC;IAES,KAAK,CAAC,OAAO,CAAC,WAA6B,EAAE,MAAe;QACpE,MAAM,WAAW,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,MAAM,CAAA;QAEzD,MAAM,sBAAe,CAAC,GAAG,CAAC,kBAAO,CAAC,WAAW,CAAC,SAAS,CAAC,EAAE,CAAC,IAAY,EAAO,EAAE;YAC9E,IAAI,IAAI,KAAK,WAAW,EAAE;gBACxB,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE,IAAI,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAA;aAC3E;YACD,OAAO,IAAI,CAAA;QACb,CAAC,CAAC,CAAA;QAEF,IAAI,CAAC,MAAM,EAAE;YACX,OAAM;SACP;QAED,MAAM,eAAe,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE,WAAW,EAAE,mBAAmB,CAAC,CAAA;QAC1F,MAAM,sBAAe,CAAC,GAAG,CAAC,0BAAgB,CAAC,kBAAO,CAAC,eAAe,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,IAAY,EAAO,EAAE;YAC9F,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;gBACzB,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAA;aACrE;iBAAM;gBACL,OAAO,IAAI,CAAA;aACZ;QACH,CAAC,CAAC,CAAA;IACJ,CAAC;CACF;AA1bD,8BA0bC;AAED,SAAS,mBAAmB,CAAC,KAAc,EAAE,aAAsB;IACjE,IAAI,aAAa,EAAE;QACjB,OAAO,KAAK,CAAC,CAAC,CAAC,CAAC,eAAe,EAAE,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,EAAE,0BAA0B,CAAC,CAAA;KACtG;IACD,OAAO,KAAK,CAAC,CAAC,CAAC,CAAC,oBAAoB,EAAE,qCAAqC,CAAC,CAAC,CAAC,CAAC,CAAC,0BAA0B,CAAC,CAAA;AAC7G,CAAC", "sourcesContent": ["import BluebirdPromise from \"bluebird-lst\"\nimport { deepAssign, Arch, AsyncTaskManager, exec, InvalidConfigurationError, log, use, getArchSuffix } from \"builder-util\"\nimport { signAsync, SignOptions } from \"electron-osx-sign\"\nimport { mkdir, readdir } from \"fs/promises\"\nimport { Lazy } from \"lazy-val\"\nimport * as path from \"path\"\nimport { copyFile, statOrNull, unlinkIfExists } from \"builder-util/out/fs\"\nimport { orIfFileNotExist } from \"builder-util/out/promise\"\nimport { AppInfo } from \"./appInfo\"\nimport { CertType, CodeSigningInfo, createKeychain, findIdentity, Identity, isSignAllowed, removeKeychain, reportError } from \"./codeSign/macCodeSign\"\nimport { DIR_TARGET, Platform, Target } from \"./core\"\nimport { AfterPackContext, ElectronPlatformName } from \"./index\"\nimport { MacConfiguration, MasConfiguration } from \"./options/macOptions\"\nimport { Packager } from \"./packager\"\nimport { chooseNotNull, PlatformPackager } from \"./platformPackager\"\nimport { ArchiveTarget } from \"./targets/ArchiveTarget\"\nimport { PkgTarget, prepareProductBuildArgs } from \"./targets/pkg\"\nimport { createCommonTarget, NoOpTarget } from \"./targets/targetFactory\"\nimport { isMacOsHighSierra } from \"./util/macosVersion\"\nimport { getTemplatePath } from \"./util/pathManager\"\nimport * as fs from \"fs/promises\"\n\nexport default class MacPackager extends PlatformPackager<MacConfiguration> {\n  readonly codeSigningInfo = new Lazy<CodeSigningInfo>(() => {\n    const cscLink = this.getCscLink()\n    if (cscLink == null || process.platform !== \"darwin\") {\n      return Promise.resolve({ keychainFile: process.env.CSC_KEYCHAIN || null })\n    }\n\n    return createKeychain({\n      tmpDir: this.info.tempDirManager,\n      cscLink,\n      cscKeyPassword: this.getCscPassword(),\n      cscILink: chooseNotNull(this.platformSpecificBuildOptions.cscInstallerLink, process.env.CSC_INSTALLER_LINK),\n      cscIKeyPassword: chooseNotNull(this.platformSpecificBuildOptions.cscInstallerKeyPassword, process.env.CSC_INSTALLER_KEY_PASSWORD),\n      currentDir: this.projectDir,\n    }).then(result => {\n      const keychainFile = result.keychainFile\n      if (keychainFile != null) {\n        this.info.disposeOnBuildFinish(() => removeKeychain(keychainFile))\n      }\n      return result\n    })\n  })\n\n  private _iconPath = new Lazy(() => this.getOrConvertIcon(\"icns\"))\n\n  constructor(info: Packager) {\n    super(info, Platform.MAC)\n  }\n\n  get defaultTarget(): Array<string> {\n    return this.info.framework.macOsDefaultTargets\n  }\n\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  protected prepareAppInfo(appInfo: AppInfo): AppInfo {\n    return new AppInfo(this.info, this.platformSpecificBuildOptions.bundleVersion, this.platformSpecificBuildOptions)\n  }\n\n  async getIconPath(): Promise<string | null> {\n    return this._iconPath.value\n  }\n\n  createTargets(targets: Array<string>, mapper: (name: string, factory: (outDir: string) => Target) => void): void {\n    for (const name of targets) {\n      switch (name) {\n        case DIR_TARGET:\n          break\n\n        case \"dmg\": {\n          // eslint-disable-next-line @typescript-eslint/no-var-requires\n          const { DmgTarget } = require(\"dmg-builder\")\n          mapper(name, outDir => new DmgTarget(this, outDir))\n          break\n        }\n\n        case \"zip\":\n          // https://github.com/electron-userland/electron-builder/issues/2313\n          mapper(name, outDir => new ArchiveTarget(name, outDir, this, true))\n          break\n\n        case \"pkg\":\n          mapper(name, outDir => new PkgTarget(this, outDir))\n          break\n\n        default:\n          mapper(name, outDir => (name === \"mas\" || name === \"mas-dev\" ? new NoOpTarget(name) : createCommonTarget(name, outDir, this)))\n          break\n      }\n    }\n  }\n\n  protected async doPack(\n    outDir: string,\n    appOutDir: string,\n    platformName: ElectronPlatformName,\n    arch: Arch,\n    platformSpecificBuildOptions: MacConfiguration,\n    targets: Array<Target>\n  ): Promise<any> {\n    switch (arch) {\n      default: {\n        return super.doPack(outDir, appOutDir, platformName, arch, platformSpecificBuildOptions, targets)\n      }\n      case Arch.universal: {\n        const x64Arch = Arch.x64\n        const x64AppOutDir = appOutDir + \"--\" + Arch[x64Arch]\n        await super.doPack(outDir, x64AppOutDir, platformName, x64Arch, platformSpecificBuildOptions, targets, false, true)\n        const arm64Arch = Arch.arm64\n        const arm64AppOutPath = appOutDir + \"--\" + Arch[arm64Arch]\n        await super.doPack(outDir, arm64AppOutPath, platformName, arm64Arch, platformSpecificBuildOptions, targets, false, true)\n        const framework = this.info.framework\n        log.info(\n          {\n            platform: platformName,\n            arch: Arch[arch],\n            [`${framework.name}`]: framework.version,\n            appOutDir: log.filePath(appOutDir),\n          },\n          `packaging`\n        )\n        const appFile = `${this.appInfo.productFilename}.app`\n        const { makeUniversalApp } = require(\"@electron/universal\")\n        await makeUniversalApp({\n          x64AppPath: path.join(x64AppOutDir, appFile),\n          arm64AppPath: path.join(arm64AppOutPath, appFile),\n          outAppPath: path.join(appOutDir, appFile),\n          force: true,\n          mergeASARs: platformSpecificBuildOptions.mergeASARs ?? true,\n          singleArchFiles: platformSpecificBuildOptions.singleArchFiles,\n          x64ArchFiles: platformSpecificBuildOptions.x64ArchFiles,\n        })\n        await fs.rm(x64AppOutDir, { recursive: true, force: true })\n        await fs.rm(arm64AppOutPath, { recursive: true, force: true })\n\n        // Give users a final opportunity to perform things on the combined universal package before signing\n        const packContext: AfterPackContext = {\n          appOutDir,\n          outDir,\n          arch,\n          targets,\n          packager: this,\n          electronPlatformName: platformName,\n        }\n        await this.info.afterPack(packContext)\n\n        await this.doSignAfterPack(outDir, appOutDir, platformName, arch, platformSpecificBuildOptions, targets)\n        break\n      }\n    }\n  }\n\n  async pack(outDir: string, arch: Arch, targets: Array<Target>, taskManager: AsyncTaskManager): Promise<any> {\n    let nonMasPromise: Promise<any> | null = null\n\n    const hasMas = targets.length !== 0 && targets.some(it => it.name === \"mas\" || it.name === \"mas-dev\")\n    const prepackaged = this.packagerOptions.prepackaged\n\n    if (!hasMas || targets.length > 1) {\n      const appPath = prepackaged == null ? path.join(this.computeAppOutDir(outDir, arch), `${this.appInfo.productFilename}.app`) : prepackaged\n      nonMasPromise = (\n        prepackaged\n          ? Promise.resolve()\n          : this.doPack(outDir, path.dirname(appPath), this.platform.nodeName as ElectronPlatformName, arch, this.platformSpecificBuildOptions, targets)\n      ).then(() => this.packageInDistributableFormat(appPath, arch, targets, taskManager))\n    }\n\n    for (const target of targets) {\n      const targetName = target.name\n      if (!(targetName === \"mas\" || targetName === \"mas-dev\")) {\n        continue\n      }\n\n      const masBuildOptions = deepAssign({}, this.platformSpecificBuildOptions, this.config.mas)\n      if (targetName === \"mas-dev\") {\n        deepAssign(masBuildOptions, this.config.masDev, {\n          type: \"development\",\n        })\n      }\n\n      const targetOutDir = path.join(outDir, `${targetName}${getArchSuffix(arch)}`)\n      if (prepackaged == null) {\n        await this.doPack(outDir, targetOutDir, \"mas\", arch, masBuildOptions, [target])\n        await this.sign(path.join(targetOutDir, `${this.appInfo.productFilename}.app`), targetOutDir, masBuildOptions, arch)\n      } else {\n        await this.sign(prepackaged, targetOutDir, masBuildOptions, arch)\n      }\n    }\n\n    if (nonMasPromise != null) {\n      await nonMasPromise\n    }\n  }\n\n  private async sign(appPath: string, outDir: string | null, masOptions: MasConfiguration | null, arch: Arch | null): Promise<void> {\n    if (!isSignAllowed()) {\n      return\n    }\n\n    const isMas = masOptions != null\n    const options = masOptions == null ? this.platformSpecificBuildOptions : masOptions\n    const qualifier = options.identity\n\n    if (!isMas && qualifier === null) {\n      if (this.forceCodeSigning) {\n        throw new InvalidConfigurationError(\"identity explicitly is set to null, but forceCodeSigning is set to true\")\n      }\n      log.info({ reason: \"identity explicitly is set to null\" }, \"skipped macOS code signing\")\n      return\n    }\n\n    const keychainFile = (await this.codeSigningInfo.value).keychainFile\n    const explicitType = options.type\n    const type = explicitType || \"distribution\"\n    const isDevelopment = type === \"development\"\n    const certificateTypes = getCertificateTypes(isMas, isDevelopment)\n\n    let identity = null\n    for (const certificateType of certificateTypes) {\n      identity = await findIdentity(certificateType, qualifier, keychainFile)\n      if (identity != null) {\n        break\n      }\n    }\n\n    if (identity == null) {\n      if (!isMas && !isDevelopment && explicitType !== \"distribution\") {\n        identity = await findIdentity(\"Mac Developer\", qualifier, keychainFile)\n        if (identity != null) {\n          log.warn(\"Mac Developer is used to sign app — it is only for development and testing, not for production\")\n        }\n      }\n\n      if (identity == null) {\n        await reportError(isMas, certificateTypes, qualifier, keychainFile, this.forceCodeSigning)\n        return\n      }\n    }\n\n    if (!isMacOsHighSierra()) {\n      throw new InvalidConfigurationError(\"macOS High Sierra 10.13.6 is required to sign\")\n    }\n\n    let filter = options.signIgnore\n    if (Array.isArray(filter)) {\n      if (filter.length == 0) {\n        filter = null\n      }\n    } else if (filter != null) {\n      filter = filter.length === 0 ? null : [filter]\n    }\n\n    const filterRe = filter == null ? null : filter.map(it => new RegExp(it))\n\n    let binaries = options.binaries || undefined\n    if (binaries) {\n      // Accept absolute paths for external binaries, else resolve relative paths from the artifact's app Contents path.\n      binaries = await Promise.all(\n        binaries.map(async destination => {\n          if (await statOrNull(destination)) {\n            return destination\n          }\n          return path.resolve(appPath, destination)\n        })\n      )\n      log.info(\"Signing addtional user-defined binaries: \" + JSON.stringify(binaries, null, 1))\n    }\n\n    const signOptions: any = {\n      \"identity-validation\": false,\n      // https://github.com/electron-userland/electron-builder/issues/1699\n      // kext are signed by the chipset manufacturers. You need a special certificate (only available on request) from Apple to be able to sign kext.\n      ignore: (file: string) => {\n        if (filterRe != null) {\n          for (const regExp of filterRe) {\n            if (regExp.test(file)) {\n              return true\n            }\n          }\n        }\n        return (\n          file.endsWith(\".kext\") ||\n          file.startsWith(\"/Contents/PlugIns\", appPath.length) ||\n          file.includes(\"/node_modules/puppeteer/.local-chromium\") ||\n          file.includes(\"/node_modules/playwright-firefox/.local-browsers\") ||\n          file.includes(\"/node_modules/playwright/.local-browsers\")\n        )\n\n        /* Those are browser automating modules, browser (chromium, nightly) cannot be signed\n          https://github.com/electron-userland/electron-builder/issues/2010\n          https://github.com/electron-userland/electron-builder/issues/5383\n          */\n      },\n      identity: identity,\n      type,\n      platform: isMas ? \"mas\" : \"darwin\",\n      version: this.config.electronVersion,\n      app: appPath,\n      keychain: keychainFile || undefined,\n      binaries,\n      timestamp: isMas ? masOptions?.timestamp : options.timestamp,\n      requirements: isMas || this.platformSpecificBuildOptions.requirements == null ? undefined : await this.getResource(this.platformSpecificBuildOptions.requirements),\n      // https://github.com/electron-userland/electron-osx-sign/issues/196\n      // will fail on 10.14.5+ because a signed but unnotarized app is also rejected.\n      \"gatekeeper-assess\": options.gatekeeperAssess === true,\n      // https://github.com/electron-userland/electron-builder/issues/1480\n      \"strict-verify\": options.strictVerify,\n      hardenedRuntime: isMas ? masOptions && masOptions.hardenedRuntime === true : options.hardenedRuntime !== false,\n    }\n\n    await this.adjustSignOptions(signOptions, masOptions)\n    log.info(\n      {\n        file: log.filePath(appPath),\n        identityName: identity.name,\n        identityHash: identity.hash,\n        provisioningProfile: signOptions[\"provisioning-profile\"] || \"none\",\n      },\n      \"signing\"\n    )\n    await this.doSign(signOptions)\n\n    // https://github.com/electron-userland/electron-builder/issues/1196#issuecomment-312310209\n    if (masOptions != null && !isDevelopment) {\n      const certType = isDevelopment ? \"Mac Developer\" : \"3rd Party Mac Developer Installer\"\n      const masInstallerIdentity = await findIdentity(certType, masOptions.identity, keychainFile)\n      if (masInstallerIdentity == null) {\n        throw new InvalidConfigurationError(`Cannot find valid \"${certType}\" identity to sign MAS installer, please see https://electron.build/code-signing`)\n      }\n\n      // mas uploaded to AppStore, so, use \"-\" instead of space for name\n      const artifactName = this.expandArtifactNamePattern(masOptions, \"pkg\", arch)\n      const artifactPath = path.join(outDir!, artifactName)\n      await this.doFlat(appPath, artifactPath, masInstallerIdentity, keychainFile)\n      await this.dispatchArtifactCreated(artifactPath, null, Arch.x64, this.computeSafeArtifactName(artifactName, \"pkg\", arch, true, this.platformSpecificBuildOptions.defaultArch))\n    }\n  }\n\n  private async adjustSignOptions(signOptions: any, masOptions: MasConfiguration | null) {\n    const resourceList = await this.resourceList\n    const customSignOptions = masOptions || this.platformSpecificBuildOptions\n    const entitlementsSuffix = masOptions == null ? \"mac\" : \"mas\"\n\n    let entitlements = customSignOptions.entitlements\n    if (entitlements == null) {\n      const p = `entitlements.${entitlementsSuffix}.plist`\n      if (resourceList.includes(p)) {\n        entitlements = path.join(this.info.buildResourcesDir, p)\n      } else {\n        entitlements = getTemplatePath(\"entitlements.mac.plist\")\n      }\n    }\n    signOptions.entitlements = entitlements\n\n    let entitlementsInherit = customSignOptions.entitlementsInherit\n    if (entitlementsInherit == null) {\n      const p = `entitlements.${entitlementsSuffix}.inherit.plist`\n      if (resourceList.includes(p)) {\n        entitlementsInherit = path.join(this.info.buildResourcesDir, p)\n      } else {\n        entitlementsInherit = getTemplatePath(\"entitlements.mac.plist\")\n      }\n    }\n    signOptions[\"entitlements-inherit\"] = entitlementsInherit\n\n    if (customSignOptions.provisioningProfile != null) {\n      signOptions[\"provisioning-profile\"] = customSignOptions.provisioningProfile\n    }\n    signOptions[\"entitlements-loginhelper\"] = customSignOptions.entitlementsLoginHelper\n  }\n\n  //noinspection JSMethodCanBeStatic\n  protected async doSign(opts: SignOptions): Promise<any> {\n    return signAsync(opts)\n  }\n\n  //noinspection JSMethodCanBeStatic\n  protected async doFlat(appPath: string, outFile: string, identity: Identity, keychain: string | null | undefined): Promise<any> {\n    // productbuild doesn't created directory for out file\n    await mkdir(path.dirname(outFile), { recursive: true })\n\n    const args = prepareProductBuildArgs(identity, keychain)\n    args.push(\"--component\", appPath, \"/Applications\")\n    args.push(outFile)\n    return await exec(\"productbuild\", args)\n  }\n\n  public getElectronSrcDir(dist: string) {\n    return path.resolve(this.projectDir, dist, this.info.framework.distMacOsAppName)\n  }\n\n  public getElectronDestinationDir(appOutDir: string) {\n    return path.join(appOutDir, this.info.framework.distMacOsAppName)\n  }\n\n  // todo fileAssociations\n  async applyCommonInfo(appPlist: any, contentsPath: string) {\n    const appInfo = this.appInfo\n    const appFilename = appInfo.productFilename\n\n    // https://github.com/electron-userland/electron-builder/issues/1278\n    appPlist.CFBundleExecutable = appFilename.endsWith(\" Helper\") ? appFilename.substring(0, appFilename.length - \" Helper\".length) : appFilename\n\n    const icon = await this.getIconPath()\n    if (icon != null) {\n      const oldIcon = appPlist.CFBundleIconFile\n      const resourcesPath = path.join(contentsPath, \"Resources\")\n      if (oldIcon != null) {\n        await unlinkIfExists(path.join(resourcesPath, oldIcon))\n      }\n      const iconFileName = \"icon.icns\"\n      appPlist.CFBundleIconFile = iconFileName\n      await copyFile(icon, path.join(resourcesPath, iconFileName))\n    }\n    appPlist.CFBundleName = appInfo.productName\n    appPlist.CFBundleDisplayName = appInfo.productName\n\n    const minimumSystemVersion = this.platformSpecificBuildOptions.minimumSystemVersion\n    if (minimumSystemVersion != null) {\n      appPlist.LSMinimumSystemVersion = minimumSystemVersion\n    }\n\n    appPlist.CFBundleIdentifier = appInfo.macBundleIdentifier\n\n    appPlist.CFBundleShortVersionString = this.platformSpecificBuildOptions.bundleShortVersion || appInfo.version\n    appPlist.CFBundleVersion = appInfo.buildVersion\n\n    use(this.platformSpecificBuildOptions.category || (this.config as any).category, it => (appPlist.LSApplicationCategoryType = it))\n    appPlist.NSHumanReadableCopyright = appInfo.copyright\n\n    if (this.platformSpecificBuildOptions.darkModeSupport) {\n      appPlist.NSRequiresAquaSystemAppearance = false\n    }\n\n    const extendInfo = this.platformSpecificBuildOptions.extendInfo\n    if (extendInfo != null) {\n      Object.assign(appPlist, extendInfo)\n    }\n  }\n\n  protected async signApp(packContext: AfterPackContext, isAsar: boolean): Promise<any> {\n    const appFileName = `${this.appInfo.productFilename}.app`\n\n    await BluebirdPromise.map(readdir(packContext.appOutDir), (file: string): any => {\n      if (file === appFileName) {\n        return this.sign(path.join(packContext.appOutDir, file), null, null, null)\n      }\n      return null\n    })\n\n    if (!isAsar) {\n      return\n    }\n\n    const outResourcesDir = path.join(packContext.appOutDir, \"resources\", \"app.asar.unpacked\")\n    await BluebirdPromise.map(orIfFileNotExist(readdir(outResourcesDir), []), (file: string): any => {\n      if (file.endsWith(\".app\")) {\n        return this.sign(path.join(outResourcesDir, file), null, null, null)\n      } else {\n        return null\n      }\n    })\n  }\n}\n\nfunction getCertificateTypes(isMas: boolean, isDevelopment: boolean): CertType[] {\n  if (isDevelopment) {\n    return isMas ? [\"Mac Developer\", \"Apple Development\"] : [\"Mac Developer\", \"Developer ID Application\"]\n  }\n  return isMas ? [\"Apple Distribution\", \"3rd Party Mac Developer Application\"] : [\"Developer ID Application\"]\n}\n"]}