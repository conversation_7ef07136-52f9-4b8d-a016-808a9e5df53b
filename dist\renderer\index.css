:root {
  --bg-color: rgba(26, 26, 26, 0.95);
  --text-color: #f0f0f0;
  --primary-color: #4a9eff;
  --secondary-color: #333;
  --accent-color: #ff4a4a;
  --success-color: #4aff4a;
  --border-radius: 12px;
  --shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

* {
  box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif;
  margin: 0;
  background-color: transparent;
  color: var(--text-color);
  font-size: 12px;
  line-height: 1.4;
  overflow: hidden;
  user-select: none;
}

.widget-container {
  position: relative;
  width: 100%;
  height: 100%;
}

/* Collapsed Widget - Minimal floating bar */
.widget-collapsed {
  display: flex;
  align-items: center;
  gap: 4px;
  background: var(--bg-color);
  backdrop-filter: blur(20px);
  border-radius: var(--border-radius);
  padding: 8px;
  box-shadow: var(--shadow);
  border: 1px solid rgba(255, 255, 255, 0.1);
  cursor: move;
  -webkit-app-region: drag;
}

.widget-collapsed .icon-btn {
  -webkit-app-region: no-drag;
}

/* Expanded Widget */
.widget-expanded {
  background: var(--bg-color);
  backdrop-filter: blur(20px);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  border: 1px solid rgba(255, 255, 255, 0.1);
  width: 300px;
  height: 200px;
  display: flex;
  flex-direction: column;
}

/* Drag Handle */
.drag-handle {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: var(--border-radius) var(--border-radius) 0 0;
  cursor: move;
  -webkit-app-region: drag;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.drag-dots {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.5);
  letter-spacing: 2px;
}

.drag-handle .icon-btn {
  -webkit-app-region: no-drag;
}

/* Text Area */
.text-area {
  flex: 1;
  padding: 12px;
}

.text-area textarea {
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: var(--text-color);
  border-radius: 8px;
  resize: none;
  font-family: inherit;
  font-size: 12px;
  line-height: 1.4;
  padding: 8px;
  transition: border-color 0.2s;
}

.text-area textarea:focus {
  outline: none;
  border-color: var(--primary-color);
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: 8px;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.02);
  border-radius: 0 0 var(--border-radius) var(--border-radius);
}

/* Icon Buttons */
.icon-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-color);
}

.icon-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.05);
}

.icon-btn:active {
  transform: scale(0.95);
}

/* Specific button styles */
.record-btn {
  background: var(--primary-color);
  color: white;
  width: 40px;
  height: 40px;
}

.record-btn:hover {
  background: #3a8aef;
  box-shadow: 0 4px 12px rgba(74, 158, 255, 0.3);
}

.record-btn.recording {
  background: var(--accent-color);
  animation: pulse 1.5s infinite;
}

.copy-btn {
  background: #28a745;
  color: white;
}

.settings-btn {
  background: #6c757d;
  color: white;
}

.settings-btn:hover {
  background: #5a6268;
}

.copy-btn:hover {
  background: #218838;
  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

.clear-btn {
  background: var(--secondary-color);
  color: var(--text-color);
}

.clear-btn:hover {
  background: #444;
}

.close-btn {
  background: var(--accent-color);
  color: white;
  width: 28px;
  height: 28px;
}

.close-btn:hover {
  background: #e53e3e;
}

.expand-btn, .collapse-btn {
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-color);
  width: 28px;
  height: 28px;
}

.expand-btn:hover, .collapse-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

/* Status Indicator */
.status-indicator {
  position: absolute;
  top: -4px;
  right: -4px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #666;
  border: 2px solid var(--bg-color);
  transition: background-color 0.3s;
}

.status-indicator.ready {
  background: #28a745;
}

.status-indicator.recording {
  background: var(--accent-color);
  animation: pulse 1s infinite;
}

.status-indicator.processing {
  background: #ffc107;
  animation: spin 1s linear infinite;
}

.status-indicator.error {
  background: var(--accent-color);
}

/* Animations */
@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Settings Modal */
.settings-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.settings-content {
  background: var(--bg-color);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  width: 320px;
  max-width: 90vw;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.settings-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.settings-header h3 {
  margin: 0;
  color: var(--text-color);
  font-size: 16px;
  font-weight: 600;
}

.close-settings-btn {
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-color);
}

.close-settings-btn:hover {
  background: var(--accent-color);
}

.settings-body {
  padding: 20px;
}

.setting-item {
  margin-bottom: 20px;
}

.setting-item label {
  display: block;
  margin-bottom: 8px;
  color: var(--text-color);
  font-size: 14px;
  font-weight: 500;
}

.shortcut-input-container {
  display: flex;
  gap: 8px;
  align-items: center;
}

.shortcut-input {
  flex: 1;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  color: var(--text-color);
  font-size: 14px;
  outline: none;
}

.shortcut-input:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(74, 158, 255, 0.2);
}

.shortcut-help {
  margin-top: 6px;
}

.shortcut-help small {
  color: rgba(240, 240, 240, 0.7);
  font-size: 12px;
}

.shortcut-help span {
  color: var(--primary-color);
  font-weight: 500;
}

.settings-footer {
  display: flex;
  gap: 12px;
  padding: 16px 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  justify-content: flex-end;
}

.btn-primary {
  background: var(--primary-color);
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-primary:hover {
  background: #3a8aef;
  transform: translateY(-1px);
}

.btn-secondary {
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-color);
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-secondary:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}