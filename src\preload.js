const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

console.log('🔧 Preload script loading...');

contextBridge.exposeInMainWorld('electronAPI', {
  // Widget controls
  hideWidget: () => ipcRenderer.send('hide-widget'),
  resizeWindow: (width, height) => ipcRenderer.send('resize-window', width, height),
  recordingCompleted: () => ipcRenderer.send('recording-completed'),

  // Audio transcription
  transcribeAudio: (audioData) => ipcRenderer.invoke('transcribe-audio', audioData),
  detectAndTranslate: (text) => ipcRenderer.invoke('detect-and-translate', text),

  // Settings
  getSettings: () => ipcRenderer.invoke('get-settings'),
  saveSettings: (settings) => ipcRenderer.invoke('save-settings', settings),

  // History and auth
  getHistory: () => ipc<PERSON>enderer.invoke('get-history'),
  login: (credentials) => ipc<PERSON>enderer.invoke('login', credentials),
  logout: () => ipc<PERSON>enderer.invoke('logout'),
  checkAuth: () => ipc<PERSON>ender<PERSON>.invoke('check-auth'),

  // Event listeners for recording
  onRecordingStarted: (callback) => {
    ipcRenderer.on('recording-started', callback);
    return () => ipcRenderer.removeListener('recording-started', callback);
  },
  onRecordingStopped: (callback) => {
    ipcRenderer.on('recording-stopped', callback);
    return () => ipcRenderer.removeListener('recording-stopped', callback);
  },
  onStartRecordingFromShortcut: (callback) => {
    ipcRenderer.on('start-recording-from-shortcut', callback);
    return () => ipcRenderer.removeListener('start-recording-from-shortcut', callback);
  },
  onStopRecordingFromShortcut: (callback) => {
    ipcRenderer.on('stop-recording-from-shortcut', callback);
    return () => ipcRenderer.removeListener('stop-recording-from-shortcut', callback);
  },

  // Other events
  onTranscriptionComplete: (callback) => {
    ipcRenderer.on('transcription-complete', (_, result) => callback(result));
    return () => ipcRenderer.removeListener('transcription-complete', callback);
  },
  onTranslationComplete: (callback) => {
    ipcRenderer.on('translation-complete', (_, result) => callback(result));
    return () => ipcRenderer.removeListener('translation-complete', callback);
  }
});

console.log('✅ Preload script loaded - electronAPI exposed');