const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

contextBridge.exposeInMainWorld('electronAPI', {
  hideWidget: () => ipcRenderer.send('hide-widget'),
  resizeWindow: (width, height) => ipcRenderer.send('resize-window', width, height),
  transcribeAudio: (audioData) => ipcRenderer.invoke('transcribe-audio', audioData),
  detectAndTranslate: (text) => ipcRenderer.invoke('detect-and-translate', text),
  getSettings: () => ipcRenderer.invoke('get-settings'),
  saveSettings: (settings) => ipcRenderer.invoke('save-settings', settings),
  recordingCompleted: () => ipcRenderer.send('recording-completed'),
  getHistory: () => ipcRenderer.invoke('get-history'),
  login: (credentials) => ipcRenderer.invoke('login', credentials),
  logout: () => ipcRenderer.invoke('logout'),
  checkAuth: () => ipcRenderer.invoke('check-auth'),
  onTranscriptionComplete: (callback) => ipcRenderer.on('transcription-complete', (_, result) => callback(result)),
  onTranslationComplete: (callback) => ipcRenderer.on('translation-complete', (_, result) => callback(result)),
  onStartRecordingFromShortcut: (callback) => ipcRenderer.on('start-recording-from-shortcut', () => callback()),
  onStopRecordingFromShortcut: (callback) => ipcRenderer.on('stop-recording-from-shortcut', () => callback())
});