{"version": 3, "file": "ProgressCallbackTransform.js", "sourceRoot": "", "sources": ["../src/ProgressCallbackTransform.ts"], "names": [], "mappings": ";;;AAAA,mCAAkC;AAWlC,MAAa,yBAA0B,SAAQ,kBAAS;IAOtD,YAA6B,KAAa,EAAmB,iBAAoC,EAAmB,UAAuC;QACzJ,KAAK,EAAE,CAAA;QADoB,UAAK,GAAL,KAAK,CAAQ;QAAmB,sBAAiB,GAAjB,iBAAiB,CAAmB;QAAmB,eAAU,GAAV,UAAU,CAA6B;QANnJ,UAAK,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;QAClB,gBAAW,GAAG,CAAC,CAAA;QACf,UAAK,GAAG,CAAC,CAAA;QAET,eAAU,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAA;IAItC,CAAC;IAED,UAAU,CAAC,KAAU,EAAE,QAAgB,EAAE,QAAa;QACpD,IAAI,IAAI,CAAC,iBAAiB,CAAC,SAAS,EAAE;YACpC,QAAQ,CAAC,IAAI,KAAK,CAAC,WAAW,CAAC,EAAE,IAAI,CAAC,CAAA;YACtC,OAAM;SACP;QAED,IAAI,CAAC,WAAW,IAAI,KAAK,CAAC,MAAM,CAAA;QAChC,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,MAAM,CAAA;QAE1B,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;QACtB,IAAI,GAAG,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,WAAW,KAAK,IAAI,CAAC,KAAK,CAAC,+BAA+B,EAAE;YAC7F,IAAI,CAAC,UAAU,GAAG,GAAG,GAAG,IAAI,CAAA;YAE5B,IAAI,CAAC,UAAU,CAAC;gBACd,KAAK,EAAE,IAAI,CAAC,KAAK;gBACjB,KAAK,EAAE,IAAI,CAAC,KAAK;gBACjB,WAAW,EAAE,IAAI,CAAC,WAAW;gBAC7B,OAAO,EAAE,CAAC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG;gBAC9C,cAAc,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,CAAC;aAC3E,CAAC,CAAA;YACF,IAAI,CAAC,KAAK,GAAG,CAAC,CAAA;SACf;QAED,QAAQ,CAAC,IAAI,EAAE,KAAK,CAAC,CAAA;IACvB,CAAC;IAED,MAAM,CAAC,QAAa;QAClB,IAAI,IAAI,CAAC,iBAAiB,CAAC,SAAS,EAAE;YACpC,QAAQ,CAAC,IAAI,KAAK,CAAC,WAAW,CAAC,CAAC,CAAA;YAChC,OAAM;SACP;QAED,IAAI,CAAC,UAAU,CAAC;YACd,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,WAAW,EAAE,IAAI,CAAC,KAAK;YACvB,OAAO,EAAE,GAAG;YACZ,cAAc,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,CAAC;SAClF,CAAC,CAAA;QACF,IAAI,CAAC,KAAK,GAAG,CAAC,CAAA;QAEd,QAAQ,CAAC,IAAI,CAAC,CAAA;IAChB,CAAC;CACF;AAtDD,8DAsDC", "sourcesContent": ["import { Transform } from \"stream\"\nimport { CancellationToken } from \"./CancellationToken\"\n\nexport interface ProgressInfo {\n  total: number\n  delta: number\n  transferred: number\n  percent: number\n  bytesPerSecond: number\n}\n\nexport class ProgressCallbackTransform extends Transform {\n  private start = Date.now()\n  private transferred = 0\n  private delta = 0\n\n  private nextUpdate = this.start + 1000\n\n  constructor(private readonly total: number, private readonly cancellationToken: CancellationToken, private readonly onProgress: (info: ProgressInfo) => any) {\n    super()\n  }\n\n  _transform(chunk: any, encoding: string, callback: any) {\n    if (this.cancellationToken.cancelled) {\n      callback(new Error(\"cancelled\"), null)\n      return\n    }\n\n    this.transferred += chunk.length\n    this.delta += chunk.length\n\n    const now = Date.now()\n    if (now >= this.nextUpdate && this.transferred !== this.total /* will be emitted on _flush */) {\n      this.nextUpdate = now + 1000\n\n      this.onProgress({\n        total: this.total,\n        delta: this.delta,\n        transferred: this.transferred,\n        percent: (this.transferred / this.total) * 100,\n        bytesPerSecond: Math.round(this.transferred / ((now - this.start) / 1000)),\n      })\n      this.delta = 0\n    }\n\n    callback(null, chunk)\n  }\n\n  _flush(callback: any): void {\n    if (this.cancellationToken.cancelled) {\n      callback(new Error(\"cancelled\"))\n      return\n    }\n\n    this.onProgress({\n      total: this.total,\n      delta: this.delta,\n      transferred: this.total,\n      percent: 100,\n      bytesPerSecond: Math.round(this.transferred / ((Date.now() - this.start) / 1000)),\n    })\n    this.delta = 0\n\n    callback(null)\n  }\n}\n"]}