directories:
  output: dist-electron
  buildResources: build
appId: com.voicewidget.app
productName: Voice to Text Widget
files:
  - filter:
      - dist/**/*
      - node_modules/**/*
      - package.json
extraResources:
  - from: .env
    to: .env
    filter:
      - '**/*'
win:
  target:
    - target: nsis
      arch:
        - x64
    - target: portable
      arch:
        - x64
  icon: build/icon.ico
  publisherName: Voice Widget Developer
  requestedExecutionLevel: asInvoker
nsis:
  oneClick: false
  allowToChangeInstallationDirectory: true
  createDesktopShortcut: true
  createStartMenuShortcut: true
  shortcutName: Voice to Text Widget
portable:
  artifactName: VoiceToTextWidget-Portable.exe
electronVersion: 24.8.8
